pageextension 50005 CustPagExt21 extends "Customer Card"
{
    layout
    {
        addafter(Blocked)
        {
            field("Approval Status"; "Approval Status")
            {
                ApplicationArea = all;
            }
            field("Customer Classification"; "Customer Classification")
            {
                ApplicationArea = all;
            }
            field(CustomerCredittype; "Customer Credit type")
            {
                ApplicationArea = all;
            }
            field("Customer Type"; "Customer Type")
            {

                ApplicationArea = ALL;

            }
            field("Direct Sales Order Allowed"; "Direct Sales Order Allowed")
            {
                ApplicationArea = all;
            }
            field("DMS Customer"; "DMS Customer")
            {
                ApplicationArea = All;

            }
            field("KD Period Joined"; "KD Period Joined")
            {
                ApplicationArea = all;
            }
            field("Commencement Date"; "Commencement Date")
            {
                ApplicationArea = all;
            }
            field("Batch Assign"; "Batch Assign")
            {
                ApplicationArea = All;
            }
            field("WHT Group"; "WHT Group")
            {
                ApplicationArea = all;
            }
            field("Interest Method"; "Interest Method")
            {
                ApplicationArea = ALL;
            }

            field("No. of Installments"; "No. of Installments")
            {
                ApplicationArea = ALL;
            }
            field("Channel No."; "Channel No.")
            {
                ApplicationArea = all;
            }
            field("Sales Area"; "Sales Area")
            {
                ApplicationArea = all;
                Visible = false;
                //b2bpksalecorr10
            }
            field("Sales Office"; "Sales Office")
            {
                ApplicationArea = all;
            }
            field("POS Customer"; "POS Customer")
            {
                ApplicationArea = all;
            }
            field("Customer Business Type"; "Customer Business Type")//PKON22M10-CR220063
            {
                ApplicationArea = all;
            }


        }
        addafter("Phone No.")
        {
            field("Alternate Phone No."; "Alternate Phone No.")
            {
                ApplicationArea = all;
            }
            field("Alternate E-Mail"; "Alternate E-Mail")
            {
                ApplicationArea = all;
            }
        }
        addafter("Balance (LCY)")
        {
            field("Credit Limit Applied (LCY)"; "Credit Limit Applied (LCY)")
            {
                ApplicationArea = all;
            }
        }
        //RFCMTDNotificationGo2solveMay2023>>>>>>
        addafter("Salesperson Code")
        {
            field("Salesperson Code 1"; Rec."Salesperson Code 1")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Second Sales Person for this customer.';
            }
        }
        //RFCMTDNotificationGo2solveMay2023<<<<<<
        //RFC 2024-006
        addafter("Direct Sales Order Allowed")
        {
            field("Customer Location"; Rec."Customer Location")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Customer Location.';
            }
            field("Customer Region"; Rec."Customer Region")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Customer Region.';
            }
            //rebate issue
            field("Fixed Asset Code"; Rec."Fixed Rebate Code")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Fixed Rebate Code.';
            }
        }
        //RFC 2024-006

        modify(Blocked)
        {
            Editable = false;
        }
        modify("Credit Limit (LCY)")
        {
            Editable = false;
            Visible = false;
        }
        modify("Responsibility Center")
        {
            Editable = false;
            Visible = false;
            //b2bpksalecorr10 end
        }
        modify("CustSalesLCY - CustProfit - AdjmtCostLCY")
        {
            Visible = ProfVisible;
            //b2bpksalecorr10 end
        }
        modify(AdjCustProfit)
        {
            Visible = ProfVisible;
            //b2bpksalecorr10 end
        }
        modify(AdjProfitPct)
        {
            Visible = ProfVisible;
            //b2bpksalecorr10 end
        }
        modify("Bill-to Customer No.")
        {
            trigger OnBeforeValidate()
            begin
                //b2bpksalecorr10 entire trigger code is added.
                IF "Bill-to Customer No." <> '' then
                    Error('Bill to customer no. field should always empty.');
            end;
        }
    }

    actions
    {
        modify(SendApprovalRequest)
        {
            trigger OnBeforeAction()
            BEGIN
                TestField("Approval Status", "Approval Status"::Open);
                TestField("Customer Type");
            END;
        }
        modify(CancelApprovalRequest)
        {
            trigger OnBeforeAction()
            begin
                TestField("Approval Status", "Approval Status"::"Pending for Approval");
            end;
        }
        addafter(SendApprovalRequest)
        {
            action("Release")
            {
                Image = ReleaseDoc;
                ApplicationArea = all;
                trigger OnAction()
                var
                /*                     RecRef: RecordRef;
                                    SharePointInt: Codeunit "Share Point Integration"; */
                begin
                    TestField("Customer Posting Group");
                    TestField("Gen. Bus. Posting Group");
                    TestField("No.");
                    TestField(Name);
                    TestField("Customer Type");
                    TestField("VAT Bus. Posting Group");

                    IF WorkflowManagement.CanExecuteWorkflow(Rec, WorkflowEventHandling.RunWorkflowOnSendVendorForApprovalcode()) then
                        error('Workflow is enabled. You can not release manually.');
                    IF "Approval Status" <> "Approval Status"::Released then BEGIN
                        "Approval Status" := "Approval Status"::Released;
                        Modify();
                    end;
                    /*                     //SharePoint>>
                                        RecRef.GETTABLE(Rec);
                                        SharePointInt.OnReleasedocumentDetails(RecRef, false);
                                        //SharePoint<< */
                end;
            }
            action("Open")
            {
                Image = Open;
                ApplicationArea = all;
                trigger OnAction()

                begin
                    IF "Approval Status" = "Approval Status"::"Pending for Approval" THEN
                        ERROR('You can not reopen the document when approval status is in %1', "Approval Status");
                    RecordRest.Reset();
                    RecordRest.SetRange(ID, 22);
                    RecordRest.SetRange("Record ID", Rec.RecordId());
                    IF RecordRest.FindFirst() THEN
                        error('This record is under in workflow process. Please cancel approval request if not required.');
                    IF "Approval Status" <> "Approval Status"::Open then BEGIN
                        "Approval Status" := "Approval Status"::Open;
                        Modify();
                    end;
                end;
            }
            action("Block")
            {
                Image = Close;
                ApplicationArea = all;
                trigger OnAction()

                begin
                    IF Blocked = Blocked::" " then BEGIN
                        IF Confirm('Do you want to block the Customer?', True, False) then BEGIN
                            Blocked := blocked::All;
                            Modify();
                        end;
                    end;
                end;
            }
            action("UnBlock")
            {
                Image = Open;
                ApplicationArea = all;
                trigger OnAction()

                begin
                    IF Blocked <> Blocked::" " then BEGIN
                        IF Confirm('Do you want to Unblock the Customer?', True, False) then BEGIN
                            Blocked := blocked::" ";
                            Modify();
                        end;
                    end;
                end;
            }
        }
        addafter("Sales Journal")
        {
            action("Online Cust. Discount") //PKONJ23
            {
                RunObject = Page "Online Cust. Discount";
                RunPageLink = "Customer No." = FIELD("No.");
                Image = List;
                ApplicationArea = all;

            }
            action("Cr. Limit Schedules")
            {
                RunObject = Page "Cust. Cr. Limit Schedules";
                RunPageLink = "Customer No." = FIELD("No.");
                Image = List;
                ApplicationArea = all;

            }
            action("Res&ponsibility Centers")
            {
                RunObject = page "Cust. Resp. Cent. Form";
                RunPageLink = "Customer No." = FIELD("No."), "Customer Name" = FIELD(Name);
                Image = List;
                ApplicationArea = all;
            }
            action("Sales Price-&Discount Group")
            {
                RunObject = Page "Customer Price/Discount Schd";
                ApplicationArea = all;
                RunPageLink = "Customer No." = field("No.");
                Image = List;
            }
            action("Van Register")
            {
                ApplicationArea = all;
                RunObject = Page "Customer Van Register";
                //PKJUL21
                //RunPageLink = "Customer No." = field("No."), "Customer Name" = field(Name);
                RunPageLink = "Customer No." = field("No.");
                //PKJUL21
            }
            action("Delete Van Register")
            {
                ApplicationArea = all;
                trigger OnAction()
                var
                    Vanregister: record "Customer Van register";
                begin
                    IF NOT CONFIRM('Do you want to delete Customer VAN Register? ', TRUE, FALSE) THEN
                        exit;
                    VANregister.reset;
                    if vanregister.findset then
                        VANREGISTER.deleteall;
                    Message('compleeted');

                end;
            }
            action("Change LogEntries")
            {
                ApplicationArea = ALL;
                trigger OnAction()
                var
                    ChangeLogEntry: Record "Change Log Entry";
                    ChangeErr: Label 'There is no Change Log entries for this Customer %1';
                BEGIN
                    ChangeLogEntry.reset;
                    ChangeLogEntry.SetRange("Table No.", 18);
                    ChangeLogEntry.SetRange("Primary Key Field 1 Value", "No.");
                    IF ChangeLogEntry.FindSet() then
                        Report.RunModal(50145, true, true, ChangeLogEntry)
                    else
                        Error(ChangeErr, "No.");
                END;

            }

            action(WHTLedgerEntries)
            {
                Image = List;
                Caption = 'WHT Ledger Entries';
                RunObject = Page WHTLedgerEntries;
                RunPageLink = "Party No." = FIELD("No.");
                ApplicationArea = All;
            }

            action(LoanList)
            {
                Image = List;
                Caption = 'Loan List';
                RunObject = Page "Loan List B2B";
                RunPageLink = "Customer No." = field("No.");
                ApplicationArea = All;
            }
            action(CustomerStatement)
            {
                Image = SendMail;
                Caption = 'Customer Statement';
                ApplicationArea = ALL;
                trigger OnAction()
                var
                    Cust: Record Customer;
                    CustMail: Report CustomerMailProcess;
                    usersetup: Record "User Setup";
                    CustSel: Record Customer;
                BEGIN
                    usersetup.get(UserId);
                    if not usersetup."Send Customer History Mail" then
                        error('You do not have permissions to perform this action.');
                    CustSel.RESET;
                    CustSel.SetRange("No.", "No.");
                    IF CustSel.FINDFIRST then
                        Report.RunModal(50553, True, False, CustSel);
                    /*
                    Clear(CustMail);
                    CustMail.SetValues("No.");
                    CustMail.RunModal();*/
                END;
            }
            action(RemoveCustomerCardResp)
            {
                Image = RemoveLine;
                ApplicationArea = All;
                trigger OnAction()
                var
                    Cust: record Customer;
                begin
                    Cust.RESET;
                    Cust.SetFilter("Responsibility Center", '<>%1', '');
                    if Cust.FindSet() then
                        Cust.ModifyAll("Responsibility Center", '');
                    message('Modified');
                end;
            }

        }
    }
    trigger OnOpenPage()
    var
        USEt: Record "User Setup";
    begin
        ////b2bpksalecorr10 all trigger
        IF USEt.GET(UserId) AND USEt."Show Item Proft & Othr Details" then
            ProfVisible := true
        else
            ProfVisible := false;
    end;

    var
        myInt: Integer;
        WorkflowManagement: codeunit "Workflow Management";
        WorkflowEventHandling: Codeunit "Workflow Event Handling";
        RecordRest: Record "Restricted Record";
        ProfVisible: Boolean;
}