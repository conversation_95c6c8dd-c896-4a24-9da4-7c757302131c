page 50940 "Customer Credit Limit Approval"
{
    PageType = Document;
    //ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "Customer Credit Approvals";
    //P.K on 15.05.2021

    layout
    {
        area(Content)
        {
            group("General")
            {
                field("No."; "No.")
                {
                    ApplicationArea = All;

                }
                field("Description"; "Description")
                {
                    ApplicationArea = All;

                }
                field("Start Date"; "Start Date")
                {
                    ApplicationArea = All;

                }
                field("End Date"; "End Date")
                {
                    ApplicationArea = All;

                }
                field(Status; Status)
                {
                    ApplicationArea = All;

                }
            }
            part(CreditLimitSchedules; "Cust. Cr. Limit Schedules")
            {
                ApplicationArea = all;
                SubPageLink = "Approv Doc No." = FIELD("No.");
            }

        }
    }

    actions
    {
        area(Processing)
        {
            action("Get Credit Limit Lines")
            {
                ApplicationArea = All;

                trigger OnAction()

                begin
                    TestField("Start Date");
                    TestField("End Date");
                    if not Confirm('Do you want to get the customer credit schedule lines ?', True, False) then
                        exit;
                    credilimitsc.RESET;
                    credilimitsc.SetFilter("Start Date", '%1..%2', "Start Date", "End Date");
                    credilimitsc.SetRange(Status, credilimitsc.Status::Open);
                    IF credilimitsc.FindSet() THEN
                        credilimitsc.ModifyALl("Approv Doc No.", "No.");
                    Message('Lines Inserted Succesfully.');
                end;
            }
            action("Get Credit Limit All Lines")
            {
                ApplicationArea = All;

                trigger OnAction()

                begin
                    TestField("Start Date");
                    TestField("End Date");
                    if not Confirm('Do you want to get the customer credit schedule lines ?', True, False) then
                        exit;
                    credilimitsc.RESET;
                    credilimitsc.SetFilter("Start Date", '%1..%2', "Start Date", "End Date");
                    //credilimitsc.SetRange(Status, credilimitsc.Status::Open);
                    IF credilimitsc.FindSet() THEN
                        credilimitsc.ModifyALl("Approv Doc No.", "No.");
                    Message('Lines Inserted Succesfully.');
                end;
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                separator(Separator1102152034)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()
                    begin
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendLSPforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        IF Status <> Status::Released then BEGIN
                            Status := Status::Released;
                            Modify();
                            credilimitsc.RESET;
                            credilimitsc.SetFilter("Approv Doc No.", "No.");
                            IF credilimitsc.FindSet() THEN
                                credilimitsc.ModifyALl(Status, credilimitsc.Status::Released);
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50363);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Modify();
                            credilimitsc.RESET;
                            credilimitsc.SetFilter("Approv Doc No.", "No.");
                            IF credilimitsc.FindSet() THEN
                                credilimitsc.ModifyALl(Status, credilimitsc.Status::Open);
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
                action("Expiry")
                {
                    ApplicationArea = all;
                    Caption = 'Expiry';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        credilimitsc.RESET;
                        credilimitsc.SetFilter("Approv Doc No.", "No.");
                        IF credilimitsc.FindSet() THEN
                            credilimitsc.ModifyALl(Status, credilimitsc.Status::Expired);
                        Message('All lines status changed to expired.');
                    end;
                }

                action(Approve)
                {
                    ApplicationArea = All;
                    Image = Action;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }
                action("Send Approval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        IF allinoneCU.CheckCLAPApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendCLAPForApproval(Rec);
                    end;
                }
                action("Cancel Approval Request")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        allinoneCU.OnCancelCLAPForApproval(Rec);
                    end;
                }
            }


        }

    }
    trigger OnAfterGetCurrRecord()
    begin
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    end;

    var
        myInt: Integer;
        RecordRest: record "Restricted Record";
        WorkflowManagement: Codeunit "Workflow Management";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
        allinoneCU: codeunit Codeunit1;
        credilimitsc: Record "Cust. Cr. Limit Schedule";
}