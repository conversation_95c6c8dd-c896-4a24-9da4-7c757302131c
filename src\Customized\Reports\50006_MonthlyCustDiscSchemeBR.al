report 50006 "Monthly Cust. Disc. SchemeBR"
{
    DefaultLayout = RDLC;
    RDLCLayout = './MonthlyCustDiscSchemeBR.rdl';
    ApplicationArea = all;
    UsageCategory = ReportsAndAnalysis;
    Caption = 'Monthly Cust. Disc. SchemeBR_50006';//PKONJ29 entire Object //PKONKD Entire Object
    dataset
    {
        dataitem(Customer; Customer)
        {
            DataItemTableView = SORTING("No.");
            RequestFilterFields = "No.", "Global Dimension 1 Filter";
            PrintOnlyIfDetail = true;

            column(Custtota; Custtota)
            {
            }
            column(PKCustTotal; PKCustTotal)
            {
            }
            column(Startdate_enddate; Format(Startdate) + '..' + Format(enddate))
            {
            }
            column(CustFilter; CustFilter)
            {
            }
            column(ItemLedFilter; ItemLedFilter)
            {
            }
            column(REPORT50274_Caption; REPORT50274_CaptionLbl)
            {
            }
            column(MonthlyCustomerDiscounts_Caption; MonthlyCustomerDiscounts_CaptionLbl)
            {
            }
            column(DateFiters_Caption; DateFiters_CaptionLbl)
            {
            }
            column(Page_Caption; Page_CaptionLbl)
            {
            }
            column(PostingDate_Caption; PostingDate_CaptionLbl)
            {
            }
            column(ItemNo_Caption; ItemNo_CaptionLbl)
            {
            }
            column(Description_Caption; Description_CaptionLbl)
            {
            }
            column(ItemLedgerEntryQuantity_Caption; ItemLedgerEntryQuantity_CaptionLbl)
            {
            }
            column(QtyEligibleforDiscount_Caption; QtyEligibleforDiscount_CaptionLbl)
            {
            }
            column(SalesAmountActual_Caption; SalesAmountActual_CaptionLbl)
            {
            }
            column(AmountVAT_Caption; AmountVAT_CaptionLbl)
            {
            }
            column(DiscPer_Caption; DiscPer_CaptionLbl)
            {
            }
            column(Discount_Caption; Discount_CaptionLbl)
            {
            }
            column(GRANDTOTAL_Caption; GRANDTOTAL_CaptionLbl)
            {
            }
            column(TotalFor_No_Name_Caption; TotalFor_No_Name_CaptionLbl)
            {
            }
            column(No_Customer; Customer."No.")
            {
            }
            column(Showdetails; Showdetails)
            { }
            column(COMPANYNAME; COMPANYNAME)
            { }
            column(TotalFor_SalesPromoQuantity; TotalFor + '   ' + 'Sales Promo Quantity')
            {
            }
            column(TotalFor_SalesReturnQuantity; TotalFor + '   ' + 'Sales Return Quantity')
            {
            }
            column(TotalFor_BigRooversQuantity; TotalFor + '   ' + 'Big Roovers Quantity')
            {
            }

            dataitem("Item Ledger Entry_2"; "Item Ledger Entry_2")
            {
                DataItemLink = "Source No." = FIELD("No.");
                DataItemLinkReference = Customer;
                DataItemTableView = SORTING("Source No.", "Cust. Sales Discount", "Gen. Prod. Posting Group", "Global Dimension 1 Code", "Branch Item No.", "Inventory Posting Group", "Posting Date") WHERE("Entry Type" = FILTER(Sale));
                RequestFilterFields = "Cust. Sales Discount", "Global Dimension 1 Code";

                trigger OnAfterGetRecord()
                begin
                    ZoneCode1 := '';

                    IF DimVal1.GET('AccLoc', "Global Dimension 1 Code") THEN
                        ZoneCode1 := DimVal."Branch Zones";
                    evapq := 1;
                    DISC.SETRANGE(DISC."Document No.", "Item Ledger Entry_2"."Cust. Sales Discount");
                    DISC.SETRANGE(DISC."Branch Zone Code", ZoneCode);
                    IF DISC.FIND('-') THEN BEGIN
                        item.SETCURRENTKEY("No.");
                        IF item.GET("Branch Item No.") THEN BEGIN
                            IF item."Discount Quantity Change" THEN
                                IF ValueEntry."Branch Prod. Discount Code" <> 'BRROOVERS' THEN
                                    evapq := (item."Spl. Quantity" / item."Standard Quantity");
                        END;
                        Discountallowed := TRUE;
                    END;
                end;

                trigger OnPreDataItem()
                begin
                    LastFieldNo := FieldNo("Cust. Sales Discount");
                    SetFilter("Posting Date", '%1..%2', Startdate, enddate);
                end;
            }
            dataitem("Value Entry"; "Value Entry")
            {
                DataItemTableView = WHERE("Item Ledger Entry Type" = FILTER(Sale), Adjustment = CONST(false), "Branch Prod. Discount Code" = filter(<> ''));
                DataItemLinkReference = Customer;
                DataItemLink = "Source No." = FIELD("No.");
                RequestFilterFields = "Branch Prod. Discount Code", "Global Dimension 1 Code";
                trigger OnAfterGetRecord()
                var
                    ItemrecL: Record Item;
                begin
                    IF BranchRebate.GET("Source No.", "Item No.") THEN BEGIN
                        IF "Sales Amount (Actual)" <> 0 THEN BEGIN
                            IF ItemrecL.GET(BranchRebate."Item No.") THEN
                                IF ItemrecL."Variant Size Multiplier" <> 0 THEN BEGIN
                                    BranchRebate."Item Ledger Entry Quantity" += (-"Invoiced Quantity");
                                    BranchRebate."Item Ledg. Entry Qty(Discount)" += (-"Invoiced Quantity" * ItemrecL."Variant Size Multiplier");
                                END ELSE BEGIN
                                    BranchRebate."Item Ledger Entry Quantity" += (-"Invoiced Quantity");
                                    BranchRebate."Item Ledg. Entry Qty(Discount)" += (-"Invoiced Quantity");
                                END;
                            BranchRebate."Sales Amount (Actual)" += "Sales Amount (Actual)";
                            BranchRebate.MODIFY;
                        END;
                    END ELSE BEGIN
                        IF "Sales Amount (Actual)" <> 0 THEN BEGIN
                            BranchRebate.INIT;
                            BranchRebate."Source No." := "Source No.";
                            BranchRebate."Item No." := "Item No.";
                            BranchRebate."Posting Date" := "Posting Date";
                            BranchRebate."Global Dimension 1 code" := "Global Dimension 1 Code";
                            IF DimVal.GET('ACCLOC', "Global Dimension 1 Code") THEN BEGIN
                                DimVal.TESTFIELD("Branch Zones");
                                RebSel.SETRANGE("Zone Code", DimVal."Branch Zones");
                                RebSel.SETRANGE("Proposed Discount Code", "Branch Prod. Discount Code");
                                IF RebSel.FINDSET THEN BEGIN
                                    REPEAT
                                        IF RebSel."Proposed Discount Code" = "Branch Prod. Discount Code" THEN
                                            BranchRebate."Prod. Discount Code" := RebSel."Actual Discount Code";
                                    UNTIL RebSel.NEXT = 0;
                                END
                                ELSE BEGIN
                                    IF "Global Dimension 1 Code" <> 'LOS' THEN
                                        BranchRebate."Prod. Discount Code" := "Branch Prod. Discount Code"
                                    else
                                        BranchRebate."Prod. Discount Code" := "Prod. Discount Code"; //PK
                                end;
                            END;
                            IF ItemrecL.GET("Item No.") THEN
                                IF ItemrecL."Variant Size Multiplier" <> 0 THEN BEGIN
                                    BranchRebate."Item Ledger Entry Quantity" := (-"Invoiced Quantity");
                                    BranchRebate."Item Ledg. Entry Qty(Discount)" := (-"Invoiced Quantity" * ItemrecL."Variant Size Multiplier");
                                END ELSE BEGIN
                                    BranchRebate."Item Ledger Entry Quantity" := -"Invoiced Quantity";
                                    BranchRebate."Item Ledg. Entry Qty(Discount)" := (-"Invoiced Quantity");
                                END;

                            BranchRebate."Sales Amount (Actual)" := "Sales Amount (Actual)";
                            BranchRebate.INSERT;

                        END;
                    END;

                    IF "Sales Amount (Actual)" = 0 THEN BEGIN
                        IF PromoBranchRebates.GET("Source No.", "Item No.") THEN BEGIN
                            PromoBranchRebates."Item Ledger Entry Quantity" += -"Invoiced Quantity";
                            PromoBranchRebates."Sales Amount (Actual)" += "Sales Amount (Actual)";
                            PromoBranchRebates.MODIFY;
                        END ELSE BEGIN
                            PromoBranchRebates.INIT;
                            PromoBranchRebates."Source No." := "Source No.";
                            PromoBranchRebates."Item No." := "Item No.";
                            PromoBranchRebates."Posting Date" := "Posting Date";
                            PromoBranchRebates."Global Dimension 1 code" := "Global Dimension 1 Code";
                            IF DimVal.GET('ACCLOC', "Global Dimension 1 Code") THEN BEGIN
                                DimVal.TESTFIELD("Branch Zones");
                                RebSel.SETRANGE("Zone Code", DimVal."Branch Zones");
                                RebSel.SETRANGE("Proposed Discount Code", "Branch Prod. Discount Code");
                                IF RebSel.FINDSET THEN BEGIN
                                    REPEAT
                                        IF RebSel."Proposed Discount Code" = "Branch Prod. Discount Code" THEN
                                            PromoBranchRebates."Prod. Discount Code" := RebSel."Actual Discount Code";
                                    UNTIL RebSel.NEXT = 0;
                                END
                                ELSE begin
                                    IF "Global Dimension 1 Code" <> 'LOS' THEN
                                        PromoBranchRebates."Prod. Discount Code" := "Branch Prod. Discount Code"
                                    else
                                        PromoBranchRebates."Prod. Discount Code" := "Prod. Discount Code"; //PK
                                end;
                            END;
                            PromoBranchRebates."Item Ledger Entry Quantity" := -"Invoiced Quantity";
                            PromoBranchRebates."Sales Amount (Actual)" := "Sales Amount (Actual)";
                            PromoBranchRebates.INSERT;
                        END;
                    END;

                    IF ("Sales Amount (Actual)" <> 0) AND ("Invoiced Quantity" > 0) THEN BEGIN
                        IF ReturnBranchRebate.GET("Source No.", "Item No.") THEN BEGIN
                            ReturnBranchRebate."Item Ledger Entry Quantity" += (-"Invoiced Quantity");
                            ReturnBranchRebate."Sales Amount (Actual)" += "Sales Amount (Actual)";
                            ReturnBranchRebate.MODIFY;
                        END ELSE BEGIN
                            //insert returns
                            ReturnBranchRebate.INIT;
                            ReturnBranchRebate."Source No." := "Source No.";
                            ReturnBranchRebate."Item No." := "Item No.";
                            ReturnBranchRebate."Posting Date" := "Posting Date";
                            ReturnBranchRebate."Global Dimension 1 code" := "Global Dimension 1 Code";
                            IF DimVal.GET('ACCLOC', "Global Dimension 1 Code") THEN BEGIN
                                DimVal.TESTFIELD("Branch Zones");
                                RebSel.SETRANGE("Zone Code", DimVal."Branch Zones");
                                RebSel.SETRANGE("Proposed Discount Code", "Branch Prod. Discount Code");
                                IF RebSel.FINDSET THEN BEGIN
                                    REPEAT
                                        IF RebSel."Proposed Discount Code" = "Branch Prod. Discount Code" THEN
                                            ReturnBranchRebate."Prod. Discount Code" := RebSel."Actual Discount Code";
                                    UNTIL RebSel.NEXT = 0;
                                END
                                ELSE begin
                                    IF "Global Dimension 1 Code" <> 'LOS' THEN
                                        ReturnBranchRebate."Prod. Discount Code" := "Branch Prod. Discount Code"
                                    else
                                        ReturnBranchRebate."Prod. Discount Code" := "Prod. Discount Code"; //PK
                                end;
                            END;
                            ReturnBranchRebate."Item Ledger Entry Quantity" := -"Invoiced Quantity";
                            ReturnBranchRebate."Sales Amount (Actual)" := "Sales Amount (Actual)";
                            ReturnBranchRebate.INSERT;
                        END;
                    END;

                end;

                trigger OnPreDataItem()
                begin
                    setcurrentkey("Item Ledger Entry Type", "Source No.", "Prod. Discount Code", "Global Dimension 1 Code", "Item No.", "Inventory Posting Group", "Posting Date");
                    LastFieldNo := FIELDNO("Prod. Discount Code");
                    SetFilter("Posting Date", '%1..%2', Startdate, enddate);
                end;
            }
            dataitem(BranchRebate; "Branch Rebates Temp")
            {
                //DataItemTableView = SORTING("Source No.", "Prod. Discount Code", "Global Dimension 1 code", "Item No.", "Posting Date");
                DataItemTableView = SORTING("Source No.", "Prod. Discount Code");//PKOMJ8commentedabove
                DataItemLink = "Source No." = FIELD("No.");
                DataItemLinkReference = Customer;
                RequestFilterFields = "Prod. Discount Code", "Global Dimension 1 code";
                column(PostingDate_BranchRebate; format(BranchRebate."Posting Date"))
                {
                }
                column(ItemNo_BranchRebate; BranchRebate."Item No.")
                {
                }
                column(descrptn; descrptn)
                {
                }
                column(ItemLedgEntryQtyDiscount_BranchRebate; BranchRebate."Item Ledg. Entry Qty(Discount)")
                {
                }
                column(ItemLedgerEntryQuantity_BranchRebate; BranchRebate."Item Ledger Entry Quantity")
                {
                }
                column(SalesAmountActual_BranchRebate; BranchRebate."Sales Amount (Actual)")
                {
                }
                column(SalesAmountActualvat105; ("Sales Amount (Actual)" * "vat%"))
                { }
                column(TotalFor_GlobalDimension1code; TotalFor + '  ' + "Global Dimension 1 code")
                {
                }
                column(GlobalDimension1code_BranchRebate; BranchRebate."Global Dimension 1 code")
                {
                }
                column(DiscPercentage; "Disc%2")
                {
                }
                /*column(SalesAmountVatDisc; Round(((("Sales Amount (Actual)" * "vat%") * "Disc%2") / 100), 1))
                {
                }*/
                column(SalesAmountVatDisc; Round(((("Sales Amount (Actual)" * "vat%") * "ItemDisc%") / 100), 1))
                {
                }//PKONJ8 commented above
                column(TotalFor_ItemNo_descrptn; TotalFor + '  ' + "Item No." + '  ' + descrptn)
                {
                }
                column(ItemLedgerEntryQuantity_evapq; ("Item Ledger Entry Quantity" * evapq))
                {
                }
                column(ItemDisc; "ItemDisc%")
                {
                }
                column(TotalFor_ProdDiscountCode_Discount_SourceNo; TotalFor + '   ' + "Prod. Discount Code" + '  ' + 'Discount' + ' ' + "Source No.")
                {
                }
                column(ItemLedgerEntryQuantity; "Item Ledger Entry Quantity")
                {
                }
                column(ABS_tq; Abs(tq))
                {
                }
                column(ABS_SalesAmountActual; Abs("Sales Amount (Actual)"))
                {
                }
                column(ABS_Disc; Abs("Disc%"))
                {
                }
                column(ROUND_DiscAmt; -Round((DiscAmt), 1))
                {
                }
                column(ABSTotalSalesPromo; -Abs(TotalSalesPromo))
                {
                }
                column(TotalSalesReturn; TotalSalesReturn)
                {
                }
                column(TotalFor_FIELDCAPTION_SourceNo; TotalFor + FieldCaption("Source No.") + "Source No.")
                {
                }
                column(SalesAmountActual; "Sales Amount (Actual)")
                {
                }
                column(ABS_SalesAmountActual_Disc100; Abs("Sales Amount (Actual)" * "Disc%" / 100))
                {
                }
                column(ROUNDTotalDiscount1; -Round(TotalDiscount, 1))
                {
                }

                column(ABS_fTotalSalesInvQty_cTotalSalesReturn_CTotalSalesPromo; Abs((fTotalSalesInvQty - cTotalSalesReturn) + (CTotalSalesPromo)))
                {
                }
                column(CTotalSalesPromo; -CTotalSalesPromo)
                {
                }
                column(ABS_cTotalSalesReturn_CTotalSalesPromoReturn; -Abs(cTotalSalesReturn + CTotalSalesPromoReturn))
                {
                }
                column(BRooversQtywithrebate4_BRooversQtywithrebate; (BRooversQtywithrebate * 4) - BRooversQtywithrebate)
                {
                }
                column(ABS_GTotalSalesInvDQty; Abs(GTotalSalesInvDQty))
                {
                }
                column(ctotalsalesamount_cTotalsalesreturnamt; ctotalsalesamount + (-cTotalsalesreturnamt))
                {
                }
                column(ctotalsalesamountwithvat; ctotalsalesamountwithvat)
                {
                }
                column(ROUNDGrandDiscount1; -Round(GrandDiscount, 1))
                {
                }
                column(ProdDiscountCode_BranchRebate; BranchRebate."Prod. Discount Code")
                {
                }

                trigger OnAfterGetRecord()
                //trigger OnPostDataItem()
                begin
                    ///PK Moved
                    K := TRUE;
                    tq := 0;
                    SalesValue := 0;
                    SalesQuantity := 0;
                    "Disc%2" := 0;
                    ItemDisc := TRUE;
                    "ItemDisc%" := 0;
                    itemSalesValue := 0;
                    itemSalesQuantity := 0;
                    CustDiscCodeQt := 0;
                    AmtWithVAT := 0;
                    TotalSalesPromo := 0;
                    TotalSalesInvQty := 0;
                    cTotalSalesInvQty := 0;
                    TotalSalesReturn := 0;
                    TotalSalesPromoReturn := 0;
                    Totalsalesreturnamt := 0;
                    BRooversQty := 0;
                    //NegativeAdjusted := FALSE;//PKONJKDJ29
                    IF NOT OldLedgerChecked THEN
                        tqOldLedger := 0;
                    AmtWithVAT := 0; //Bug fixed 02/03/2020 SAA //added 5/29/2021//PKON290521

                    //BranchRebate, GroupHeader(2) - OnPreSection()
                    //IF CurrReport.SHOWOUTPUT THEN BEGIN
                    /*NegativeAdjusted := FALSE;
                    TotalLinesRecreated := 0;
                    RebatePeriodRec.RESET;
                    RebatePeriodRec.SETFILTER(RebatePeriodRec."Start Date", '<=%1', Startdate);
                    RebatePeriodRec.SETFILTER(RebatePeriodRec."End Date", '>=%1', enddate);
                    IF RebatePeriodRec.FINDFIRST THEN BEGIN
                        //SalesHeader.SETFILTER("Document Type",'<>%1',SalesHeader."Document Type"::"Credit Memo");
                        SalesHeaderCreated.SETRANGE("Sell-to Customer No.", Customer."No.");
                        SalesHeaderCreated.SETRANGE("Rebate Period Code", RebatePeriodRec.Code);
                        IF SalesHeaderCreated.FINDFIRST THEN BEGIN
                            RecreateSalesline := TRUE;
                            SalesLineRec.RESET;
                            SalesLineRec.SETRANGE(SalesLineRec."Document No.", SalesHeaderCreated."No.");
                            IF SalesLineRec.FINDSET THEN
                                SalesLineRec.DELETEALL;
                        END;
                    END;*/
                    //Pk on 27.04. for test
                    /*   DISC.SETRANGE(DISC."Document No.", BranchRebate."Prod. Discount Code");
                      IF NOT DISC.FIND('-') THEN
                          CurrReport.SHOWOUTPUT(FALSE); */


                    //BranchRebate, GroupHeader(4) - OnPreSection()
                    ZoneCode := '';

                    //BranchRebate, Body (7) - OnPreSection()
                    //added 2/5/2018
                    tq := tq + (-BranchRebate."Item Ledg. Entry Qty(Discount)");
                    tamount := tamount + (BranchRebate."Sales Amount (Actual)" * evapq);
                    IF item.GET("Item No.") THEN;
                    //"vat%" := 1;//PKON310521
                    IF vatpostinggrp.GET(Customer."VAT Bus. Posting Group", item."VAT Prod. Posting Group") THEN
                        IF vatpostinggrp."VAT %" <> 0 THEN BEGIN
                            IF custdisc.GET(BranchRebate."Prod. Discount Code") THEN BEGIN
                                IF custdisc.Vatable THEN
                                    "vat%" := 1.075
                                ELSE
                                    "vat%" := 1;
                            END;
                        END ELSE
                            "vat%" := 1;

                    tvat := tvat + ((BranchRebate."Sales Amount (Actual)" * "vat%") * evapq);
                    IF item.GET("Item No.") THEN
                        descrptn := item.Description
                    ELSE
                        descrptn := '';
                    //END;
                    // cTotalSalesInvQty += (-BranchRebate."Item Ledger Entry Quantity"*evapq);
                    //CTotalSalesPromo += TotalSalesPromo;
                    //if (BranchRebate."Item Ledger Entry Quantity"*evapq) < 0  then
                    //cTotalSalesReturn += TotalSalesReturn;

                    //NYO 17/07/2018
                    ItemLedQty := 0;

                    //IF item.GET("Item No.") THEN
                    //IF item."Variant Size" = item."Variant Size"::Big THEN
                    //  ItemLedQty:="Item Ledger Entry Quantity"/4
                    //ELSE
                    ItemLedQty := "Item Ledger Entry Quantity";
                    //PK--->
                    // BranchRebate, GroupFooter(9) - OnPreSection()
                    //IF NOT (Showdivisions) THEN
                    //CurrReport.SHOWOUTPUT:=FALSE;

                    IF DimVal.GET('AccLoc', "Global Dimension 1 code") THEN
                        ZoneCode := DimVal."Branch Zones";

                    //IF DISC.GET(BranchRebate."Prod. Discount Code",minqty,ZoneCode) THEN;
                    DISC.SETRANGE(DISC."Document No.", BranchRebate."Prod. Discount Code");
                    DISC.SETRANGE(DISC."Branch Zone Code", ZoneCode);
                    IF DISC.FIND('-') THEN BEGIN
                        item.SETCURRENTKEY("No.");
                        IF item.GET("Item No.") THEN BEGIN
                            IF InventoryPostingGroupL.GET(item."Inventory Posting Group") THEN
                                BRDiscLocal := InventoryPostingGroupL."Branch Prod. Discount Code"
                            ELSE
                                BRDiscLocal := '';
                            IF item."Discount Quantity Change" THEN
                                IF (BranchRebate."Prod. Discount Code" <> 'BRROOVERS') AND
                                (BRDiscLocal <> 'BRROOVERS') THEN
                                    evapq := (item."Spl. Quantity" / item."Standard Quantity");
                        END;
                        Discountallowed := TRUE;
                    END;


                    //IF CurrReport.SHOWOUTPUT THEN BEGIN
                    DivQt := DivQt + (BranchRebate."Item Ledger Entry Quantity" * evapq);
                    IF K THEN BEGIN
                        //K:=FALSE;
                        IF Discountallowed THEN BEGIN
                            //cTotalSalesInvQty += ("Item Ledger Entry Quantity"*evapq);
                            K := FALSE;
                            cdiscount := "Prod. Discount Code";
                            BranchRebate2.SETCURRENTKEY(
                            "Source No.", "Prod. Discount Code", "Global Dimension 1 code", "Item No.", "Posting Date");
                            BranchRebate2.SETRANGE("Source No.", "Source No.");
                            BranchRebate2.SETRANGE("Prod. Discount Code", "Prod. Discount Code");
                            BranchRebate2.SETFILTER("Posting Date", '%1..%2', Startdate, enddate);
                            IF BranchRebate2.FINDSET THEN
                                REPEAT
                                    IF BranchRebate2."Sales Amount (Actual)" <> 0 THEN BEGIN
                                        SalesValue += BranchRebate2."Sales Amount (Actual)";
                                        SalesQuantity += (BranchRebate2."Item Ledger Entry Quantity" * evapq);
                                    END;
                                UNTIL BranchRebate2.NEXT = 0;
                            DISC2.RESET;
                            DISC2.SETRANGE(DISC2."Document No.", "Prod. Discount Code");
                            DISC2.SETRANGE(DISC2."Branch Zone Code", ZoneCode);//PK Code changed variable 
                            IF DISC2.FIND('-') THEN BEGIN
                                REPEAT
                                    IF ABS((SalesQuantity)) >= DISC2."Minimum Quantity" THEN BEGIN
                                        //Bug Fix RKD 26-06-20 >>
                                        IF OnlineDiscExist("Value Entry"."Source No.") THEN
                                            "Disc%2" := OnlineDiscPer
                                        ELSE
                                            "Disc%2" := DISC2."Discount %";
                                        //"Disc%2" := DISC2."Discount %";
                                        //Bug Fix RKD 26-06-20 >>
                                    END ELSE BEGIN
                                        IF OnlineDiscExist("Value Entry"."Source No.") THEN
                                            "Disc%2" := OnlineDiscPer
                                    END;
                                // ELSE
                                // EXIT;
                                UNTIL DISC2.NEXT = 0;
                            END;
                        END;
                    END;


                    //BranchRebate, GroupFooter (9) - OnPreSection()
                    IF DimVal.GET('AccLoc', "Global Dimension 1 code") THEN
                        ZoneCode := DimVal."Branch Zones";

                    DivQt := DivQt + (BranchRebate."Item Ledger Entry Quantity" * evapq);
                    IF K THEN BEGIN
                        //K:=FALSE;
                        IF Discountallowed THEN BEGIN
                            //cTotalSalesInvQty += ("Item Ledger Entry Quantity"*evapq);
                            K := FALSE;
                            cdiscount := "Prod. Discount Code";
                            BranchRebate2.SETCURRENTKEY(
                            "Source No.", "Prod. Discount Code", "Global Dimension 1 code", "Item No.", "Posting Date");
                            BranchRebate2.SETRANGE("Source No.", "Source No.");
                            BranchRebate2.SETRANGE("Prod. Discount Code", "Prod. Discount Code");
                            BranchRebate2.SETFILTER("Posting Date", '%1..%2', Startdate, enddate);
                            IF BranchRebate2.FINDSET THEN
                                REPEAT
                                    IF BranchRebate2."Sales Amount (Actual)" <> 0 THEN BEGIN
                                        SalesValue += BranchRebate2."Sales Amount (Actual)";
                                        SalesQuantity += (BranchRebate2."Item Ledger Entry Quantity" * evapq);
                                    END;
                                UNTIL BranchRebate2.NEXT = 0;

                            DISC2.RESET;
                            DISC2.SETRANGE("Document No.", "Prod. Discount Code");
                            DISC2.SETRANGE("Branch Zone Code", ZoneCode);//Code Changed from Disc to Disc2
                            IF DISC2.FIND('-') THEN BEGIN
                                REPEAT
                                    IF ABS((SalesQuantity)) >= DISC2."Minimum Quantity" THEN BEGIN
                                        "Disc%2" := DISC2."Discount %";
                                    END;
                                UNTIL DISC2.NEXT = 0;
                            END;
                        END;
                    END;


                    //added to calculate sums
                    IF "Item Ledger Entry Quantity" > 0 THEN BEGIN
                        IF ItemRec.GET("Item No.") THEN
                            IF ItemRec."Variant Size" = ItemRec."Variant Size"::Big THEN BEGIN
                                TotalSalesInvQty += ("Item Ledger Entry Quantity");
                                //tBRooversQty += ("Item Ledger Entry Quantity"/4);
                                BRooversQty += ("Item Ledger Entry Quantity");
                            END ELSE BEGIN
                                TotalSalesInvQty += ("Item Ledger Entry Quantity" * evapq);
                            END;
                    END;

                    ZoneCode1 := '';

                    IF DimVal1.GET('AccLoc', "Global Dimension 1 code") THEN
                        ZoneCode1 := DimVal."Branch Zones";

                    evapq := 1;
                    //Moved to


                    //Add BranchRebate, GroupFooter (10) - Onpresection Begin-pk

                    OldSaleslineLineNo := 0;

                    //Check if Rebate Credit Memo was created for source no. and prod. discount code.
                    //IF CreateCreditMemo AND CurrReport.SHOWOUTPUT AND NOT RecreateSalesline THEN BEGIN
                    IF CreateCreditMemo AND NOT RecreateSalesline THEN BEGIN
                        IF item.GET("Item No.") THEN;
                        RebatePeriodRec.RESET;
                        RebatePeriodRec.SETFILTER(RebatePeriodRec."Start Date", '<=%1', Startdate);
                        RebatePeriodRec.SETFILTER(RebatePeriodRec."End Date", '>=%1', enddate);
                        IF RebatePeriodRec.FINDFIRST THEN BEGIN
                            //SalesHeader.SETFILTER("Document Type",'<>%1',SalesHeader."Document Type"::"Credit Memo");
                            SalesHeaderCreated.SETRANGE("Sell-to Customer No.", Customer."No.");
                            SalesHeaderCreated.SETRANGE("Rebate Period Code", RebatePeriodRec.Code);
                            IF SalesHeaderCreated.FINDFIRST THEN BEGIN
                                salessetup.GET();
                                IF SalesHeaderCreated."Posting No. Series" = '' THEN BEGIN
                                    SalesHeaderCreated.VALIDATE("Posting No. Series", salessetup."Posted Credit Memo Nos.");
                                    SalesHeaderCreated.MODIFY;
                                END;
                            END;
                        END ELSE
                            ERROR(Text50002);
                    END;

                    //IF CurrReport.SHOWOUTPUT THEN BEGIN Pk on 27.04
                    Itemqt := Itemqt + (BranchRebate."Item Ledg. Entry Qty(Discount)");
                    IF ItemDisc THEN BEGIN
                        //K:=FALSE;
                        IF Discountallowed THEN BEGIN
                            ItemDisc := FALSE;
                            cdiscount := "Prod. Discount Code";
                            BranchRebate2.SETCURRENTKEY(
                            "Source No.", "Prod. Discount Code", "Global Dimension 1 code", "Item No.", "Posting Date");
                            BranchRebate2.SETRANGE("Source No.", "Source No.");
                            BranchRebate2.SETRANGE("Prod. Discount Code", "Prod. Discount Code");
                            BranchRebate2.SETFILTER("Posting Date", '%1..%2', Startdate, enddate);
                            IF BranchRebate2.FINDSET THEN
                                REPEAT
                                    IF BranchRebate2."Sales Amount (Actual)" <> 0 THEN BEGIN
                                        item.SETCURRENTKEY("No.");
                                        IF item.GET(BranchRebate2."Item No.") THEN BEGIN
                                            IF InventoryPostingGroupLL.GET(item."Inventory Posting Group") THEN
                                                BRDiscLocalL := InventoryPostingGroupLL."Branch Prod. Discount Code"
                                            ELSE
                                                BRDiscLocalL := '';
                                            IF item."Discount Quantity Change" THEN BEGIN
                                                IF (BranchRebate2."Prod. Discount Code" <> 'BRROOVERS') OR
                                                  (BRDiscLocalL <> 'BRROOVERS') THEN
                                                    IF NOT CheckRebateSelection(BranchRebate2) THEN
                                                        //IF item."Discount Quantity Change" THEN
                                                        evapqall := (item."Spl. Quantity" / item."Standard Quantity") ELSE
                                                        evapqall := 1;
                                            END ELSE
                                                evapqall := 1;
                                        END;
                                        itemSalesValue += BranchRebate2."Sales Amount (Actual)";
                                        //commented since multiple has been done on inserting to branch rebate table from value and item ledger entry
                                        //itemSalesQuantity+=(BranchRebate2."Item Ledger Entry Quantity" * evapqall);
                                        //commented 03/10/18
                                        //itemSalesQuantity+=(BranchRebate2."Item Ledger Entry Quantity");
                                        itemSalesQuantity += (BranchRebate2."Item Ledg. Entry Qty(Discount)");
                                    END;
                                UNTIL BranchRebate2.NEXT = 0;

                            DISC2.RESET;
                            DISC2.SETRANGE(DISC2."Document No.", "Prod. Discount Code");
                            DISC2.SETRANGE(DISC2."Branch Zone Code", ZoneCode);
                            IF DISC2.FIND('-') THEN BEGIN
                                REPEAT
                                    /*BEGIN
                                         IF ABS((itemSalesQuantity)) >= DISC2."Minimum Quantity" THEN BEGIN
                                             "ItemDisc%" := DISC2."Discount %";
                                         END;*/
                                    IF ABS((itemSalesQuantity)) >= DISC2."Minimum Quantity" THEN BEGIN
                                        //Bug Fix RKD 26-06-20 >>
                                        IF OnlineDiscExist("Value Entry"."Source No.") THEN
                                            "ItemDisc%" := OnlineDiscPer
                                        ELSE
                                            "ItemDisc%" := DISC2."Discount %";
                                        //"Disc%2" := DISC2."Discount %";
                                        //Bug Fix RKD 26-06-20 >>
                                    END ELSE BEGIN
                                        IF OnlineDiscExist("Value Entry"."Source No.") THEN
                                            "ItemDisc%" := OnlineDiscPer
                                        // ELSE
                                        // EXIT;
                                    END
                                UNTIL DISC2.NEXT = 0;
                            END;
                        END;
                    END;

                    //CurrReport.SHOWOUTPUT("ItemDisc%"<>0);
                    IF NOT (ShowItems) THEN;
                    //CurrReport.SHOWOUTPUT := FALSE;
                    //END;Pk on 27.04
                    //CurrReport.SHOWOUTPUT("Disc%2"<>0);
                    AmtWithVAT += "Sales Amount (Actual)" * "vat%";//added 5/29/2021
                    IF "ItemDisc%" = 0 THEN;
                    //CurrReport.SHOWOUTPUT := FALSE;


                    //IF ("Sales Amount (Actual)" < 0) AND CurrReport.SHOWOUTPUT THEN //Pk On 27.04.2021
                    IF ("Sales Amount (Actual)" < 0) THEN
                        TotalNegativeAmounts += ROUND(((("Sales Amount (Actual)") * "ItemDisc%") / 100), 1);


                    //IF PrintExcel AND (CurrReport.SHOWOUTPUT) THEN BEGIN //Pk On 27.04.2021
                    IF PrintExcel THEN BEGIN
                        RowNo += 1;
                        /*EnterCell(RowNo, 1, FORMAT(TotalFor + '  ' + "Item No."), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 5, FORMAT("Item Ledg. Entry Qty(Discount)"), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 7, FORMAT(("Sales Amount (Actual)" * "vat%")), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 8, FORMAT("ItemDisc%"), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 9, FORMAT(ROUND(((("Sales Amount (Actual)" * "vat%") * "ItemDisc%") / 100), 1)), FALSE, FALSE, FALSE);*///Balu
                    END;

                    //Create Credit Memos

                    //NYO 15/09/2016
                    IF RebateHistoryRec.GET(BranchRebate."Global Dimension 1 code", PeriodCode, BranchRebate."Source No.",
                       BranchRebate."Item No.") THEN BEGIN
                        RebateHistoryRec.CALCFIELDS("Posted Cr. Memo No.");
                        IF RebateHistoryRec."Posted Cr. Memo No." <> '' THEN;
                        //CurrReport.SHOWOUTPUT := FALSE;
                    END;

                    IF LastCustomerNoCreatedfor <> "Source No." THEN BEGIN
                        //IF CreateCreditMemo AND CurrReport.SHOWOUTPUT THEN BEGIN
                        IF CreateCreditMemo THEN BEGIN
                            LastCustomerNoCreatedfor := '';//SAA3.0
                            salessetup.GET;
                            IF NOT RecreateSalesline THEN BEGIN
                                SalesHeader.INIT;
                                SalesHeader."Document Type" := SalesHeader."Document Type"::"Credit Memo";
                                SalesHeader."No." := NoSeriesMgt.GetNextNo(salessetup."Credit Memo Nos.", TODAY, FALSE);
                                SalesHeader."Sell-to Customer No." := "Source No.";
                                SalesHeader."Bill-to Customer No." := "Source No.";
                                SalesHeader."Cr. Memo Sub Doc. Type" := SalesHeader."Cr. Memo Sub Doc. Type"::Branch; //HO1.0

                                SalesHeader."Posting Date" := enddate;
                                salessetup.GET();
                                SalesHeader.VALIDATE("Return Receipt No. Series", salessetup."Posted Return Receipt Nos.");
                                RebatePeriodRec.RESET;
                                RebatePeriodRec.SETFILTER(RebatePeriodRec."Start Date", '<=%1', Startdate);
                                RebatePeriodRec.SETFILTER(RebatePeriodRec."End Date", '>=%1', enddate);
                                IF RebatePeriodRec.FINDFIRST THEN BEGIN
                                    SalesHeader."Rebate Period Code" := RebatePeriodRec.Code;
                                    SalesHeader."Printable Comment 1" := RebatePeriodRec.Description + ' ' + 'REBATE';
                                END;
                                //SalesHeader.VALIDATE("User Wise Resp Centr", ResponsibilityCenter);//PKONJU7.2
                                //SalesHeader.VALIDATE("Responsibility Center", ResponsibilityCenter);//PKONJU7.2
                                SalesHeader."Cr. Memo Reason Type" := SalesHeader."Cr. Memo Reason Type"::Others;
                                SalesHeader."Cr. Memo Stock Type" := SalesHeader."Cr. Memo Stock Type"::"Non-Items";
                                SalesHeader."Reason Code" := 'REBATE';
                                SalesHeader.VALIDATE("Document Date", enddate);
                                IF SalesHeader.INSERT THEN BEGIN
                                    TotalCreditMemoCreated += 1;
                                    Noseriesline.SETRANGE("Series Code", salessetup."Credit Memo Nos.");
                                    //IF noseriesline.GET(PurchSetup."IPO FILE NO.")
                                    IF Noseriesline.FINDFIRST THEN BEGIN
                                        Noseriesline.VALIDATE("Last No. Used", SalesHeader."No.");
                                        Noseriesline.MODIFY;
                                    END;

                                END;
                                SalesHeader.VALIDATE("Sell-to Customer No.", "Source No.");
                                //SalesHeader.VALIDATE("User Wise Resp Centr", ResponsibilityCenter);//PKONJU7.2
                                SalesHeader.VALIDATE("Responsibility Center", ResponsibilityCenter);//PKONJU7.2
                                ShiptoAddress.SETRANGE("Customer No.", Customer."No.");
                                ShiptoAddress.SETFILTER(Code, '<>%1', '');
                                IF ShiptoAddress.FINDFIRST THEN BEGIN
                                    SalesHeader."Ship-to Code" := ShiptoAddress.Code;
                                    SalesHeader."Ship-to Name" := ShiptoAddress.Name;
                                    SalesHeader."Ship-to Address" := ShiptoAddress.Address;
                                    SalesHeader."Ship-to City" := ShiptoAddress.City;
                                    SalesHeader."Ship-to Name 2" := ShiptoAddress."Name 2";
                                    SalesHeader."Ship-to Address 2" := ShiptoAddress."Address 2";
                                END;
                                //B2BPKJ3
                                RespcDim.get(ResponsibilityCenter);
                                SalesHeader.Validate("Shortcut Dimension 1 Code", RespcDim."Global Dimension 1 Code");
                                SalesHeader.Validate("Shortcut Dimension 2 Code", RespcDim."Global Dimension 2 Code");
                                SalesHeader.MODIFY;
                                LastCustomerNoCreatedfor := "Source No.";//SAA3.0
                                                                         //SAA3.0 <<
                            END;
                        END;
                    END;

                    //IF CreateCreditMemo AND CurrReport.SHOWOUTPUT AND NOT RecreateSalesline THEN BEGIN
                    IF CreateCreditMemo AND NOT RecreateSalesline THEN BEGIN
                        RebateHistoryRec.INIT;
                        RebateHistoryRec.VALIDATE("Accounting Location", BranchRebate."Global Dimension 1 code");
                        RebateHistoryRec.VALIDATE("Rebate Period", PeriodCode);
                        RebateHistoryRec."Customer No." := BranchRebate."Source No.";
                        RebateHistoryRec."Item No." := BranchRebate."Item No.";
                        RebateHistoryRec.Quantity := BranchRebate."Item Ledg. Entry Qty(Discount)";
                        RebateHistoryRec.Amount := BranchRebate."Sales Amount (Actual)";
                        RebateHistoryRec."Rebate Amount" := ROUND(((("Sales Amount (Actual)" * "vat%") * "ItemDisc%") / 100), 1);
                        RebateHistoryRec."Rebate Persentage" := "ItemDisc%";
                        RebateHistoryRec."Credit Memo Created" := TRUE;
                        RebateHistoryRec."Cr. Memo No." := SalesHeader."No.";
                        //RKD >>
                        IF "Global Dimension 1 code" <> 'LOS' THEN
                            RebateHistoryRec."Rebate Group" := "Value Entry"."Branch Prod. Discount Code"
                        ELSE
                            RebateHistoryRec."Rebate Group" := "Value Entry"."Prod. Discount Code";
                        //RKD <<

                        IF RebateHistoryRec.INSERT THEN;

                        IF RebateHistoryRec.GET(BranchRebate."Global Dimension 1 code", PeriodCode, BranchRebate."Source No.",
                           BranchRebate."Item No.") THEN BEGIN
                            //RebateHistoryRec.INIT;
                            RebateHistoryRec.VALIDATE("Accounting Location", BranchRebate."Global Dimension 1 code");
                            RebateHistoryRec.VALIDATE("Rebate Period", PeriodCode);
                            RebateHistoryRec."Customer No." := BranchRebate."Source No.";
                            RebateHistoryRec."Item No." := BranchRebate."Item No.";
                            RebateHistoryRec.Quantity := BranchRebate."Item Ledg. Entry Qty(Discount)";
                            RebateHistoryRec.Amount := BranchRebate."Sales Amount (Actual)";
                            RebateHistoryRec."Rebate Amount" := ROUND(((("Sales Amount (Actual)" * "vat%") * "ItemDisc%") / 100), 1);
                            RebateHistoryRec."Rebate Persentage" := "ItemDisc%";
                            RebateHistoryRec."Credit Memo Created" := TRUE;
                            //RKD >>
                            IF "Global Dimension 1 code" <> 'LOS' THEN
                                RebateHistoryRec."Rebate Group" := "Value Entry"."Branch Prod. Discount Code"
                            ELSE
                                RebateHistoryRec."Rebate Group" := "Value Entry"."Prod. Discount Code";
                            //RKD <<

                            //RebateHistoryRec."Cr. Memo No.":=SalesHeader."No.";
                            RebateHistoryRec.MODIFY;
                        END

                    END;

                    //NYO 15/09/2016
                    //IF CreateCreditMemo AND CurrReport.SHOWOUTPUT AND RecreateSalesline THEN BEGIN
                    IF CreateCreditMemo AND RecreateSalesline THEN BEGIN
                        IF RebateHistoryRec.GET(BranchRebate."Global Dimension 1 code", PeriodCode, BranchRebate."Source No.",
                           BranchRebate."Item No.") THEN BEGIN
                            //RebateHistoryRec.INIT;
                            RebateHistoryRec.VALIDATE("Accounting Location", BranchRebate."Global Dimension 1 code");
                            RebateHistoryRec.VALIDATE("Rebate Period", PeriodCode);
                            RebateHistoryRec."Customer No." := BranchRebate."Source No.";
                            RebateHistoryRec."Item No." := BranchRebate."Item No.";
                            RebateHistoryRec.Quantity := BranchRebate."Item Ledg. Entry Qty(Discount)";
                            RebateHistoryRec.Amount := BranchRebate."Sales Amount (Actual)";
                            RebateHistoryRec."Rebate Amount" := ROUND(((("Sales Amount (Actual)" * "vat%") * "ItemDisc%") / 100), 1);
                            RebateHistoryRec."Rebate Persentage" := "ItemDisc%";
                            RebateHistoryRec."Credit Memo Created" := TRUE;
                            //RebateHistoryRec."Cr. Memo No.":=SalesHeader."No.";
                            //RKD >>
                            IF "Global Dimension 1 code" <> 'LOS' THEN
                                RebateHistoryRec."Rebate Group" := "Value Entry"."Branch Prod. Discount Code"
                            ELSE
                                RebateHistoryRec."Rebate Group" := "Value Entry"."Prod. Discount Code";
                            //RKD <<

                            RebateHistoryRec.MODIFY;
                        END
                        ELSE BEGIN
                            RebateHistoryRec.INIT;
                            RebateHistoryRec.VALIDATE("Accounting Location", BranchRebate."Global Dimension 1 code");
                            RebateHistoryRec.VALIDATE("Rebate Period", PeriodCode);
                            RebateHistoryRec."Customer No." := BranchRebate."Source No.";
                            RebateHistoryRec."Item No." := BranchRebate."Item No.";
                            RebateHistoryRec.Quantity := BranchRebate."Item Ledg. Entry Qty(Discount)";
                            RebateHistoryRec.Amount := BranchRebate."Sales Amount (Actual)";
                            RebateHistoryRec."Rebate Amount" := ROUND(((("Sales Amount (Actual)" * "vat%") * "ItemDisc%") / 100), 1);
                            RebateHistoryRec."Rebate Persentage" := "ItemDisc%";
                            RebateHistoryRec."Credit Memo Created" := TRUE;
                            RebateHistoryRec."Cr. Memo No." := SalesHeaderCreated."No.";
                            //RKD >>
                            IF "Global Dimension 1 code" <> 'LOS' THEN
                                RebateHistoryRec."Rebate Group" := "Value Entry"."Branch Prod. Discount Code"
                            ELSE
                                RebateHistoryRec."Rebate Group" := "Value Entry"."Prod. Discount Code";
                            //RKD <<

                            IF RebateHistoryRec.INSERT THEN;
                        END;
                    END;
                    //NYO 15/09/2016


                    //IF CreateCreditMemo AND CurrReport.SHOWOUTPUT AND ("Sales Amount (Actual)" > 0) THEN BEGIN
                    //IF CreateCreditMemo AND ("Sales Amount (Actual)" > 0) THEN BEGIN //PKONKD29 Removed ABS
                    IF CreateCreditMemo AND ("Sales Amount (Actual)" <> 0) AND ("ItemDisc%" <> 0) AND ("vat%" <> 0) THEN BEGIN //PKONJU8
                        SalesLine2.INIT;
                        SalesLine2.Type := 1;
                        SalesLine2."Document Type" := SalesLine2."Document Type"::"Credit Memo";
                        IF RecreateSalesline THEN BEGIN
                            SalesLine2."Document No." := SalesHeaderCreated."No.";
                            SalesLine2."Line No." += 10000;
                            SalesLine2."Sell-to Customer No." := SalesHeaderCreated."Sell-to Customer No.";
                            SalesLine2.INSERT;
                            SalesLine2.VALIDATE("No.", CreditMemACCT);
                            SalesLine2."Shortcut Dimension 1 Code" := SalesHeaderCreated."Shortcut Dimension 1 Code";
                            SalesLine2."Shortcut Dimension 2 Code" := SalesHeaderCreated."Shortcut Dimension 2 Code";
                            //SalesLine2.VALIDATE(Quantity,ABS("Item Ledger Entry Quantity"));
                            //SalesLine2.VALIDATE("Unit Price",ROUND(((("Sales Amount (Actual)"{*"vat%"1.05})*"Disc%")/100),1)/ABS(CustDiscCodeQt));
                            IF ABS("Item Ledg. Entry Qty(Discount)") <> 0 THEN
                                SalesLine2.VALIDATE("Unit Price", ABS(ROUND((((("Sales Amount (Actual)" * "vat%") * "ItemDisc%") / 100)), 1) / ABS(
                                "Item Ledg. Entry Qty(Discount)") / "vat%"))//PKONKD29 ABS Added
                            //END
                            ELSE
                                SalesLine2.VALIDATE("Unit Price", 0);
                            //SalesLine2.VALIDATE(Amount,ROUND(((("Sales Amount (Actual)"{*"vat%"1.05})*"ItemDisc%")/100),1));
                            SalesLine2.VALIDATE(Quantity, "Item Ledg. Entry Qty(Discount)");//PKONKD29 Removed ABS
                        END ELSE BEGIN
                            SalesLine2."Document No." := SalesHeader."No.";
                            SalesLine2."Line No." += 10000;
                            SalesLine2."Sell-to Customer No." := SalesHeader."Sell-to Customer No.";
                            SalesLine2.INSERT;
                            SalesLine2.VALIDATE("No.", CreditMemACCT);
                            SalesLine2."Shortcut Dimension 1 Code" := SalesHeader."Shortcut Dimension 1 Code";
                            SalesLine2."Shortcut Dimension 2 Code" := SalesHeader."Shortcut Dimension 2 Code";

                            //SalesLine2.VALIDATE("Unit Price",ROUND(((("Sales Amount (Actual)"{*"vat%"1.05})*"Disc%")/100),1)/ABS(CustDiscCodeQt));
                            IF ABS("Item Ledg. Entry Qty(Discount)") <> 0 THEN
                                SalesLine2.VALIDATE("Unit Price", ABS(ROUND(((("Sales Amount (Actual)") * "ItemDisc%") / 100), 1) / ABS(
                                "Item Ledg. Entry Qty(Discount)")))//PKONKD29 ABS Added
                            ELSE
                                SalesLine2.VALIDATE("Unit Price", 0);

                            //SalesLine2.VALIDATE(Amount,ROUND(((("Sales Amount (Actual)"{*"vat%"1.05})*"ItemDisc%")/100),1));
                            SalesLine2.VALIDATE(Quantity, "Item Ledg. Entry Qty(Discount)" * evapq);//PKONKD29 Removed ABS
                            //SalesLine2.Quantity:=ABS(CustDiscCodeQt);
                            //SalesLine2.Quantity:=ABS(itemSalesQuantity);
                        END;
                        SalesLine2.VALIDATE("Cust. Discount Code", "Prod. Discount Code");
                        IF item.GET("Item No.") THEN BEGIN
                            SalesLine2.VALIDATE("Gen. Prod. Posting Group", item."Gen. Prod. Posting Group");

                            GetDims(item, SalesLine2);
                        END;

                        IF RecreateSalesline THEN BEGIN
                            SalesLine2.VALIDATE(SalesLine2."Shortcut Dimension 1 Code", SalesHeaderCreated."Shortcut Dimension 1 Code");
                            SalesLine2.VALIDATE(SalesLine2."Shortcut Dimension 2 Code", SalesHeaderCreated."Shortcut Dimension 2 Code");
                        END ELSE BEGIN
                            SalesLine2.VALIDATE(SalesLine2."Shortcut Dimension 1 Code", SalesHeader."Shortcut Dimension 1 Code");
                            SalesLine2.VALIDATE(SalesLine2."Shortcut Dimension 2 Code", SalesHeader."Shortcut Dimension 2 Code");
                        END;
                        IF "vat%" <> 1 THEN // bugfix saa 02-20-2020
                            SalesLine2.VALIDATE(SalesLine2."VAT Prod. Posting Group", 'VATABLE')
                        ELSE
                            SalesLine2.VALIDATE(SalesLine2."VAT Prod. Posting Group", 'NOTVATABLE');
                        SalesLine2."Description 2" := RebatePeriodRec.Description + ' ' + 'Dist. Discount';
                        SalesLine2.VALIDATE("Return Reason Code", 'DDBR');
                        //PKONKD29
                        /*IF NOT NegativeAdjusted THEN BEGIN
                            //PKONJ11
                            RebateSalesLine.Reset();
                            RebateSalesLine.SetRange("Document No.", SalesLine2."Document No.");
                            RebateSalesLine.SetFilter(Amount, '>%1', Abs(TotalNegativeAmounts));
                            if RebateSalesLine.FindFirst then begin
                                RebateSalesLine.Validate("Unit Price", (RebateSalesLine.Amount
                                - (Abs(TotalNegativeAmounts))) / RebateSalesLine.Quantity);
                                if RebateSalesLine.Modify then
                                    NegativeAdjusted := true;
                            end;
                        END;*///PKONKD29
                        SalesLine2.MODIFY;
                    END;
                    //Add BranchRebate, GroupFooter (10) - Onpresection end-pk
                    //BranchRebate, GroupFooter (11) - OnPreSection()
                    "Disc%" := 0.0;
                    DiscAmt := 0.0;
                    DISC.RESET;
                    DISC.SETRANGE(DISC."Document No.", BranchRebate."Prod. Discount Code");
                    DISC.SETRANGE(DISC."Branch Zone Code", ZoneCode);
                    IF DISC.FIND('-') THEN BEGIN
                        Discountallowed := TRUE;
                        REPEAT
                            IF ABS(BranchRebate."Item Ledg. Entry Qty(Discount)") < minqty THEN BEGIN
                                "Disc%" := 0.0;
                            END ELSE BEGIN
                                IF ABS(BranchRebate."Item Ledg. Entry Qty(Discount)") >= DISC."Minimum Quantity" THEN BEGIN
                                    "Disc%" := DISC."Discount %";
                                    //DiscAmt := -(BranchRebate."Sales Amount (Actual)" * "vat%" * "Disc%" / 100);
                                    DiscAmt := -((AmtWithVAT * "Disc%" / 100)); //PKONadded 5/29/2021
                                    CustDiscCodeQt += ABS(BranchRebate."Item Ledg. Entry Qty(Discount)");//SAA3.0
                                END;
                            END;
                        UNTIL DISC.NEXT = 0;
                    END ELSE
                        Discountallowed := FALSE;


                    //BranchRebate, GroupFooter (11) - OnPostSection()
                    DISC.RESET;
                    DISC.SETRANGE("Document No.", BranchRebate."Prod. Discount Code");
                    DISC.SETRANGE("Branch Zone Code", ZoneCode);
                    IF DISC.FIND('-') THEN BEGIN
                        Discountallowed := TRUE;
                        REPEAT
                            IF (ABS(tq) >= DISC."Minimum Quantity") THEN BEGIN
                                "Disc%" := DISC."Discount %";
                                DiscAmt := -((BranchRebate."Sales Amount (Actual)") * "vat%" * "Disc%" / 100);
                            END
                        UNTIL DISC.NEXT = 0;
                    END;
                    //

                    //BranchRebate, Footer (13) - OnPreSection()
                    GTotalSalesInvDQty := BranchRebate."Item Ledg. Entry Qty(Discount)";

                    IF Discountallowed THEN BEGIN
                        TotalDiscount := TotalDiscount + DiscAmt;
                        GrandDiscount := GrandDiscount + DiscAmt;
                        IF ("Disc%" <> 0.0) THEN BEGIN
                            PromoBranchRebates.SETRANGE("Source No.", "Source No.");
                            PromoBranchRebates.SETRANGE(PromoBranchRebates."Prod. Discount Code", BranchRebate."Prod. Discount Code");
                            IF PromoBranchRebates.FINDSET THEN
                                REPEAT
                                    IF PromoBranchRebates."Item Ledger Entry Quantity" > 0 THEN BEGIN
                                        TotalSalesPromo += PromoBranchRebates."Item Ledger Entry Quantity";
                                        CTotalSalesPromo += PromoBranchRebates."Item Ledger Entry Quantity";
                                    END ELSE BEGIN
                                        TotalSalesPromoReturn += PromoBranchRebates."Item Ledger Entry Quantity";
                                        CTotalSalesPromoReturn += PromoBranchRebates."Item Ledger Entry Quantity";
                                    END;
                                UNTIL PromoBranchRebates.NEXT = 0;

                            cTotalSalesInvQty += ((BranchRebate."Item Ledger Entry Quantity" * evapq) - ((BRooversQty * 4) - BRooversQty));
                            //TotalSalesInvQty;
                            fTotalSalesInvQty += cTotalSalesInvQty;
                            GTotalSalesInvQty += (BranchRebate."Item Ledger Entry Quantity"); //cTotalSalesInvQty;
                                                                                              //GTotalSalesInvDQty+= BranchRebate."Item Ledg. Entry Qty(Discount)";
                            ReturnBranchRebate.SETRANGE("Source No.", "Source No.");
                            ReturnBranchRebate.SETRANGE("Prod. Discount Code", BranchRebate."Prod. Discount Code");
                            IF ReturnBranchRebate.FINDSET THEN
                                REPEAT
                                    IF ReturnBranchRebate."Item Ledger Entry Quantity" < 0 THEN BEGIN
                                        TotalSalesReturn += ReturnBranchRebate."Item Ledger Entry Quantity";
                                        cTotalSalesReturn += ReturnBranchRebate."Item Ledger Entry Quantity";
                                        //Totalsalesreturnamt += ReturnBranchRebate."Sales Amount (Actual)"
                                    END;
                                UNTIL ReturnBranchRebate.NEXT = 0;
                            BRooversQtywithrebate += BRooversQty;
                            ctotalsalesamount += ((BranchRebate."Sales Amount (Actual)") + Totalsalesreturnamt);
                            ctotalsalesamountwithvat += ((BranchRebate."Sales Amount (Actual)") * "vat%")
                        END;
                    END;

                    //IF (ABS(tq)=SalesQuantity) THEN
                    cdiscount := '';
                    PKSalesAmnt += "Sales Amount (Actual)";
                    PKCustTotal := "Sales Amount (Actual)" * ("ItemDisc%" / 100);
                    IF HideZero then // checks if "ItemDisc%" is zero and HideZero is true
                        IF "ItemDisc%" = 0 then
                            CurrReport.skip;//PKON31052021
                end;

                trigger OnPostDataItem()
                var
                    rebat: Record "Branch Rebates Temp";
                    rebat2: Record "Branch Rebates Temp";
                    PrevGrp: code[20];
                begin
                    /*Clear(PrevGrp);
                    rebat.RESET;
                    rebat.SetCurrentKey("Source No.", "Prod. Discount Code");
                    rebat.SetRange("Source No.", Customer."No.");
                    IF rebat.FindSet() then
                        repeat
                            IF PrevGrp <> rebat."Prod. Discount Code" THEN begin
                                rebat2.RESET;
                                rebat2.SetCurrentKey("Source No.", "Prod. Discount Code");
                                rebat2.SetRange("Source No.", Customer."No.");
                                rebat2.SetRange("Prod. Discount Code", rebat."Prod. Discount Code");
                                IF rebat2.FindSet() then BEGIN
                                    rebat2.CalcSums("Sales Amount (Actual)", "Item Ledg. Entry Qty(Discount)");

                                    DISC2.RESET;
                                    DISC2.SETRANGE(DISC2."Document No.", rebat2."Prod. Discount Code");
                                    DISC2.SETRANGE(DISC2."Branch Zone Code", ZoneCode);
                                    IF DISC2.FIND('-') THEN BEGIN
                                        REPEAT
                                            IF ABS((rebat2."Item Ledg. Entry Qty(Discount)")) >= DISC2."Minimum Quantity" THEN BEGIN
                                                IF OnlineDiscExist("Value Entry"."Source No.") THEN
                                                    "ItemDisc%" := OnlineDiscPer
                                                ELSE
                                                    "ItemDisc%" := DISC2."Discount %";
                                            END ELSE BEGIN
                                                IF OnlineDiscExist("Value Entry"."Source No.") THEN
                                                    "ItemDisc%" := OnlineDiscPer
                                            END
                                        UNTIL DISC2.NEXT = 0;
                                    end;
                                end;
                                Custtota += rebat2."Item Ledg. Entry Qty(Discount)" * ("ItemDisc%" / 100);
                                PrevGrp := rebat."Prod. Discount Code";
                                Message('%1', Custtota);
                            end;
                        until rebat.next = 0;*///PK
                end;

                trigger OnPreDataItem()
                begin
                    //Rebate
                    LastFieldNo := FieldNo("Prod. Discount Code");
                    SetFilter("Posting Date", '%1..%2', Startdate, enddate);
                    //BranchRebate, GroupHeader(3) - OnPreSection()
                    //IF CurrReport.SHOWOUTPUT THEN BEGIN

                end;
            }

            trigger OnAfterGetRecord()
            begin

                ///PK Moved
                NegativeAdjusted := FALSE;//PKONJKDJ29
                K := TRUE;
                tq := 0;
                SalesValue := 0;
                SalesQuantity := 0;
                "Disc%2" := 0;
                ItemDisc := TRUE;
                "ItemDisc%" := 0;
                itemSalesValue := 0;
                itemSalesQuantity := 0;
                CustDiscCodeQt := 0;
                TotalSalesPromo := 0;
                TotalSalesInvQty := 0;
                cTotalSalesInvQty := 0;
                TotalSalesReturn := 0;
                TotalSalesPromoReturn := 0;
                Totalsalesreturnamt := 0;
                BRooversQty := 0;
                PKCustTotal := 0;
                PKSalesAmnt := 0;
                Custtota := 0;
                IF NOT OldLedgerChecked THEN
                    tqOldLedger := 0;
                //Customer, GroupHeader(3) - OnPreSection()
                // CurrReport.SHOWOUTPUT :=
                //  CurrReport.TOTALSCAUSEDBY = Customer.FIELDNO("No.");

                //IF CurrReport.SHOWOUTPUT THEN BEGIN
                //TotalDiscount := 0; pk on 07/01/2021
                RecreateSalesline := FALSE;
                /*
                BranchRebate.DELETEALL;
                ReturnBranchRebate.DELETEALL;
                PromoBranchRebates.DELETEALL;*/ //pk on 01.06.2021
                TotalNegativeAmounts := 0;
                //END;
                /*cTotalSalesInvQty := 0;
                CTotalSalesPromo := 0;
                cTotalSalesReturn := 0;
                 */
                //CurrReport.CREATETOTALS({CTotalSalesPromo}cTotalSalesInvQty,{cTotalSalesReturn,}tq,tamount,tvat);

                //SORTING(No.) WHERE(Customer Disc. Group=FILTER(DISTRIBU|WD|RD))

                CustDiscount.SETRANGE("Customer No.", "No.");
                CustDiscount.SETFILTER(Status, '%1', CustDiscount.Status::Released);
                CustDiscount.SETFILTER("Sales Discount Group", '%1|%2|%3|%4|%5|%6', 'DISTRIBU', 'WD', 'RD', 'ASL', 'OVCORP', 'TOTCUST');
                //CustDiscount.SETFILTER("Sales Discount Group", '%1|%2|%3', 'DISTRIBU', 'WD', 'RD');
                IF CustDiscount.ISEMPTY THEN
                    CurrReport.SKIP;

                //Minus the negative amounts from a salesline amount
                /*
                                //IF NOT NegativeAdjusted THEN BEGIN
                                RebateSalesLine.Reset();
                                RebateSalesLine.SetRange("Document No.", SalesLine2."Document No.");
                                RebateSalesLine.SetFilter(Amount, '>%1', Abs(TotalNegativeAmounts));
                                if RebateSalesLine.FindFirst then begin
                                    RebateSalesLine.Validate("Unit Price", (RebateSalesLine.Amount
                                    - (Abs(TotalNegativeAmounts))) / RebateSalesLine.Quantity);
                                    if RebateSalesLine.Modify then
                                        NegativeAdjusted := true;
                                end;
                                // END;*///PKON 06.04.2021

                TotalFor_No_Name_CaptionLbl := 'TotalFor    ' + "No." + ':    ' + Name + ' =========>';
                //Customer, Footer (6) - OnPreSection()
                BranchRebate.DELETEALL;
                ReturnBranchRebate.DELETEALL;
                PromoBranchRebates.DELETEALL;
                TotalDiscount := 0; //pk on 07/01/2021
            end;

            trigger OnPostDataItem()
            begin

            end;

            trigger OnPreDataItem()
            begin
                // IF ResponsibilityCenter <> '' then
                //    SetFilter("Responsibility Center", ResponsibilityCenter);
                /*
                                //Customer, GroupHeader(3) - OnPreSection()
                                // CurrReport.SHOWOUTPUT :=
                                //  CurrReport.TOTALSCAUSEDBY = Customer.FIELDNO("No.");

                                //IF CurrReport.SHOWOUTPUT THEN BEGIN
                                TotalDiscount := 0;
                                RecreateSalesline := FALSE;
                                BranchRebate.DELETEALL;
                                ReturnBranchRebate.DELETEALL;
                                PromoBranchRebates.DELETEALL;
                                TotalNegativeAmounts := 0;
                                //END;
                                /*cTotalSalesInvQty := 0;
                                CTotalSalesPromo := 0;
                                cTotalSalesReturn := 0;
                                 */
                //CurrReport.CREATETOTALS({CTotalSalesPromo}cTotalSalesInvQty,{cTotalSalesReturn,}tq,tamount,tvat);
            end;
        }
    }

    requestpage
    {
        SaveValues = true;
        layout
        {
            area(Content)
            {

                group(Options)
                {
                    Caption = 'Options';
                    field(Startdate; Startdate)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Start Date';
                    }
                    field(enddate; enddate)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'End Date';
                    }
                    field(PeriodCode; PeriodCode)
                    {
                        Caption = 'Period Code';
                        ApplicationArea = Basic, Suite;
                        TableRelation = "Rebate Period Codes".Code;
                    }
                    field(HideZero; HideZero)
                    {
                        Caption = 'Hide Zero Rebate Lines.';
                        ApplicationArea = Basic, Suite;
                    }
                    field(CreditMemACCT; CreditMemACCT)
                    {
                        Caption = 'Enter Credit Memo G/L Acct';
                        ApplicationArea = Basic, Suite;
                        TableRelation = "G/L Account";
                    }
                    field(ResponsibilityCenter; ResponsibilityCenter)
                    {
                        Caption = 'Responsibility Centre';
                        ApplicationArea = Basic, Suite;
                        TableRelation = "Responsibility Center";
                    }
                    /*
                    field(ShowItems; ShowItems)
                    {
                        Caption = 'Show Item Totals';
                        ApplicationArea = Basic, Suite;
                    }*/
                    field(CreateCreditMemo; CreateCreditMemo)
                    {
                        Caption = 'Create Credit Memo';
                        ApplicationArea = Basic, Suite;
                        trigger OnValidate()
                        begin
                            IF Usersetup.GET(USERID) THEN
                                IF NOT Usersetup."Create Rebate Cred. Memos" THEN
                                    ERROR(Text50000);
                            //SAA3.0 >>
                            IF CreateCreditMemo THEN BEGIN
                                IF ResponsibilityCenter = '' THEN
                                    ERROR('Responsibility Center must not been blank');
                            END;
                            //SAA3.0 <<
                        end;
                    }
                    field(Showdetails; Showdetails)
                    {
                        Caption = 'Show details';
                        ApplicationArea = Basic, Suite;
                    }

                }
            }
        }

        actions
        {
        }
    }

    labels
    {
    }

    trigger OnInitReport()
    begin
        Discountallowed := false;
        TotalDiscount := 0;
        DocType := 3;
    end;

    trigger OnPostReport()
    begin
        if TotalCreditMemoCreated <> 0 then
            Message('A total of %1 Credit Memos were sucessfully created', TotalCreditMemoCreated);

        if TotalLinesRecreated <> 0 then
            Message('Credit Memos LINES were sucessfully re-created', TotalLinesRecreated);


    end;

    trigger OnPreReport()
    begin
        minqty := 0;
        "mindis%" := 0;
        Datefilter := "Value Entry".GetFilter("Posting Date");
        CustFilter := Customer.GetFilters;
        ItemLedFilter := "Value Entry".GetFilters;
        //BUFilter := Customer.GETFILTER(
        if Datefilter <> '' then
            Error('Posting Date Filter must be blank, please fill in the Start Date and End Date in the Options TAB');
        //nyo
        if PeriodCode = '' then
            Error(Text50003);

        IF RebatePeriodCodes.GET(PeriodCode) THEN BEGIN
            Startdate := RebatePeriodCodes."Start Date";
            enddate := RebatePeriodCodes."End Date";
        END;
        //nyo
        if (Startdate = 0D) or (enddate = 0D) then
            Error('Start Date or End Date must not be blank');

        if CreateCreditMemo then
            if CreditMemACCT = '' then
                Error('Enter The Credit Memo G/L Account No.');
    end;

    var
        PrintExcel: Boolean;
        TotalFor: Label 'Total for ';
        Text50000: Label 'You do not have approval to create Rebate Credit Memos.';
        Text50001: Label 'The Rebate Credit Memo %1  for %2 discount, %3, and customer %4';
        Text50002: Label 'The Rebate period needs to be created in the Rebate Period Codes setup.';
        Text50003: Label 'Enter Period Code';
        Text003: Label 'Monthly Cust. Disc. SchemeBR';
        tvat: Decimal;
        tamount: Decimal;
        LastFieldNo: Integer;
        FooterPrinted: Boolean;
        minqty: Decimal;
        "Disc%": Decimal;
        Cust: Record Customer;
        RebateHistoryRec: Record "Rebate History Table";
        RebatePeriodCodes: Record "Rebate Period Codes";
        CustName: Text[100];
        "Discount%": Decimal;
        "mindis%": Decimal;
        "currdisc%": Decimal;
        "prevdis%": Decimal;
        Showdetails: Boolean;
        Datefilter: Text[30];
        Discountallowed: Boolean;
        DiscAmt: Decimal;
        TotalDiscount: Decimal;
        GrandDiscount: Decimal;
        CustFilter: Text[250];
        ItemLedFilter: Text[250];
        BUFilter: Text[250];
        salesline: Record "Sales Line";
        SourceNo: Code[20];
        DocType: Option Quote,"Order",Invoice,"Credit Memo","Blanket Order";
        t: Boolean;
        evapq: Decimal;
        item: Record Item;
        tq: Decimal;
        "vat%": Decimal;
        item2: Record Item;
        descrptn: Text[100];
        ValueEntry: Record "Value Entry";
        CustDiscount: Record "Customer Sales Price/Discount";
        InventoryPostingGroupLL: Record "Inventory Posting Group";
        InventoryPostingGroupL: Record "Inventory Posting Group";
        BRDiscLocalL: Code[20];
        BRDiscLocal: Code[20];
        DISC2: Record "Item Sales Disc. Qty. Lines";

        SalesValue: Decimal;
        SalesQuantity: Decimal;
        "Disc%2": Decimal;
        K: Boolean;
        Startdate: Date;
        enddate: Date;
        cdiscount: Code[20];
        Showdivisions: Boolean;
        RowNo: Integer;
        ColumnNo: Integer;
        "ItemDisc%": Decimal;
        ItemDisc: Boolean;
        ShowItems: Boolean;
        itemSalesValue: Decimal;
        itemSalesQuantity: Decimal;
        SalesHeader: Record "Sales Header";
        SalesLine2: Record "Sales Line";
        NoSeriesMgt: Codeunit NoSeriesManagement;
        salessetup: Record "Sales & Receivables Setup";
        DISC: Record "Item Sales Disc. Qty. Lines";
        SalesLineREC: Record "Sales Line";
        CreateCreditMemo: Boolean;
        CreditMemACCT: Code[20];
        DivQt: Decimal;
        Usersetup: Record "User Setup";
        ResponsibilityCenter: Code[20];
        CustDiscCodeQt: Decimal;
        AmtWithVAT: Decimal;
        custdisc: Record "Customer Group Discount";
        RebSel: Record "Rebate Selection";
        RebatePeriodRec: Record "Rebate Period Codes";
        Itemqt: Decimal;
        TotalCreditMemoCreated: Integer;
        LastCustomerNoCreatedfor: Code[20];
        ShiptoAddress: Record "Ship-to Address";
        TotalNegativeAmounts: Decimal;
        RebateSalesLine: Record "Sales Line";
        NegativeAdjusted: Boolean;
        Noseriesline: Record "No. Series Line";
        SalesHeaderCreated: Record "Sales Header";
        RecreateSalesline: Boolean;
        OldSaleslineLineNo: Integer;
        TotalLinesRecreated: Integer;
        evapqall: Decimal;
        vatpostinggrp: Record "VAT Posting Setup";
        tqOldLedger: Decimal;
        tamtOldledger: Decimal;
        tvatOldLedger: Decimal;
        OldLedgerChecked: Boolean;
        CombinedVEntry: Record "Branch Rebates Temp" temporary;
        BranchRebate2: Record "Branch Rebates Temp";
        ZoneCode: Code[10];
        ZoneCode1: Code[10];
        DimVal: Record "Dimension Value";
        DimVal1: Record "Dimension Value";
        PeriodCode: Code[10];
        InvValueentry: Record "Value Entry";
        ReturnValueentry: Record "Value Entry";
        PromoValueentry: Record "Value Entry";
        TotalSalesInvQty: Decimal;
        TotalSalesReturn: Decimal;
        TotalSalesPromo: Decimal;
        CTotalSalesPromo: Decimal;
        cTotalSalesInvQty: Decimal;
        PromoBranchRebates: Record "Branch Rebates Temp" temporary;
        ItemRec: Record Item;
        cTotalSalesReturn: Decimal;
        newt: Decimal;
        ctotalsalesamountwithvat: Decimal;
        ctotalsalesamount: Decimal;
        Totalsalesreturnamt: Decimal;
        cTotalsalesreturnamt: Decimal;
        GTotalSalesInvQty: Decimal;
        GTotalSalesInvDQty: Decimal;
        CTotalSalesPromoReturn: Decimal;
        TotalSalesPromoReturn: Decimal;
        ReturnBranchRebate: Record "Branch Rebates Temp" temporary;
        BRooversQty: Decimal;
        BRooversQtywithrebate: Decimal;
        ActualBrooversqty: Decimal;
        fTotalSalesInvQty: Decimal;
        tBRooversQty: Decimal;
        ItemLedQty: Decimal;
        REPORT50274_CaptionLbl: Label 'REPORT 50274';
        MonthlyCustomerDiscounts_CaptionLbl: Label 'Monthly Customer Discounts';
        DateFiters_CaptionLbl: Label 'Date Fiters:';
        Page_CaptionLbl: Label 'Page';
        PostingDate_CaptionLbl: Label 'Posting Date';
        ItemNo_CaptionLbl: Label 'Item No.';
        Description_CaptionLbl: Label 'Description';
        ItemLedgerEntryQuantity_CaptionLbl: Label 'Item Ledger Entry Quantity';
        QtyEligibleforDiscount_CaptionLbl: Label 'Qty. Eligible for Discount';
        SalesAmountActual_CaptionLbl: Label 'Sales Amount (Actual)';
        AmountVAT_CaptionLbl: Label 'Amount + VAT';
        DiscPer_CaptionLbl: Label 'Disc. %';
        Discount_CaptionLbl: Label 'Discount';
        GRANDTOTAL_CaptionLbl: Label 'GRAND TOTAL ======>';
        TotalFor_No_Name_CaptionLbl: text[250];
        DefaultDimension: Record "Default Dimension";
        DimensionSetEntry: Record "Dimension Set Entry";
        TempDimensionSetEntry: Record "Dimension Set Entry" temporary;
        DIMMgmt: Codeunit DimensionManagement;
        OnlineDiscPer: Decimal;
        OnlineDiscAmt: Decimal;
        TempDimSetEntry: Record "Dimension Set Entry" temporary;
        HideZero: Boolean;
        PKCustTotal: Decimal;
        PKSalesAmnt: Decimal;
        RespcDim: Record "Responsibility Center";
        Custtota: Decimal;



    procedure CheckRebateSelection(BranchRebateRec: Record "Branch Rebates Temp"): Boolean
    var
        RSelect: Boolean;

    begin
        WITH BranchRebateRec DO BEGIN
            RSelect := FALSE;
            IF DimVal.GET('ACCLOC', "Global Dimension 1 code") THEN BEGIN
                DimVal.TESTFIELD("Branch Zones");
                RebSel.SETRANGE("Zone Code", DimVal."Branch Zones");
                RebSel.SETRANGE("Actual Discount Code", "Prod. Discount Code");
                IF RebSel.FINDSET THEN BEGIN
                    REPEAT
                        //IF RebSel."Proposed Discount Code" = "Prod. Discount Code" THEN BEGIN
                        //BranchRebate."Prod. Discount Code":=RebSel."Actual Discount Code";
                        RSelect := TRUE;
                    //END;
                    UNTIL RebSel.NEXT = 0;
                    EXIT(RSelect);
                END
            END;
        END;
    end;
    /*
        procedure CalculateSalesQty()
        begin
            BEGIN
                TotalSalesInvQty := 0;
                TotalSalesReturn := 0;
                TotalSalesPromo := 0;
                //get the total invoiced quantity
                InvValueentry.RESET;
                InvValueentry.SETCURRENTKEY("Item Ledger Entry Type", "Source No.", "Prod. Discount Code", "Global Dimension 1 Code",
                  "Item No.", "Inventory Posting Group", "Posting Date");
                InvValueentry.SETFILTER("Item Ledger Entry Type", '%1', InvValueentry."Item Ledger Entry Type"::Sale);
                InvValueentry.SETRANGE("Source No.", Customer."No.");//"Value Entry"."Source No.");
                InvValueentry.SETRANGE("Branch Prod. Discount Code", BranchRebate."Prod. Discount Code");
                InvValueentry.SETRANGE("Global Dimension 1 Code", BranchRebate."Global Dimension 1 code");
                IF "Value Entry".GETFILTER("Item No.") <> '' THEN
                    InvValueentry.SETFILTER("Item No.", '%1', "Value Entry".GETFILTER("Item No."));
                InvValueentry.SETFILTER("Posting Date", '%1..%2', Startdate, enddate);
                InvValueentry.SETFILTER("Document Type", '%1', InvValueentry."Document Type"::"Sales Invoice");
                IF InvValueentry.FINDSET THEN
                    REPEAT
                    BEGIN
                        //InvValueentry.CALCSUMS("Invoiced Quantity");
                        TotalSalesInvQty += InvValueentry."Invoiced Quantity";
                    END;
                    UNTIL InvValueentry.NEXT = 0;
                //get the total sales promo quantity
                InvValueentry.RESET;
                InvValueentry.SETCURRENTKEY("Item Ledger Entry Type", "Source No.", "Prod. Discount Code", "Global Dimension 1 Code",
                  "Item No.", "Inventory Posting Group", "Posting Date");
                InvValueentry.SETFILTER("Item Ledger Entry Type", '%1', InvValueentry."Item Ledger Entry Type"::Sale);
                InvValueentry.SETRANGE("Source No.", Customer."No.");//"Value Entry"."Source No.");
                InvValueentry.SETRANGE("Branch Prod. Discount Code", BranchRebate."Prod. Discount Code");
                InvValueentry.SETRANGE("Global Dimension 1 Code", BranchRebate."Global Dimension 1 code");
                IF "Value Entry".GETFILTER("Item No.") <> '' THEN
                    InvValueentry.SETFILTER("Item No.", '%1', "Value Entry".GETFILTER("Item No."));
                InvValueentry.SETFILTER("Posting Date", '%1..%2', Startdate, enddate);
                InvValueentry.SETFILTER("Document Type", '%1', InvValueentry."Document Type"::"Sales Invoice");
                InvValueentry.SETFILTER("Item Ledger Entry Quantity", '<%1', 0);
                InvValueentry.SETFILTER("Sales Amount (Actual)", '%1', 0);
                IF InvValueentry.FINDSET THEN
                    REPEAT
                        //InvValueentry.CALCSUMS("Invoiced Quantity");
                        TotalSalesPromo += InvValueentry."Invoiced Quantity";
                    UNTIL InvValueentry.NEXT = 0;

                //get the total sales return quantity
                InvValueentry.RESET;
                InvValueentry.SETCURRENTKEY("Item Ledger Entry Type", "Source No.", "Prod. Discount Code", "Global Dimension 1 Code",
                  "Item No.", "Inventory Posting Group", "Posting Date");
                InvValueentry.SETFILTER("Item Ledger Entry Type", '%1', InvValueentry."Item Ledger Entry Type"::Sale);
                InvValueentry.SETRANGE("Source No.", Customer."No."); //"Value Entry"."Source No.");
                InvValueentry.SETRANGE("Branch Prod. Discount Code", BranchRebate."Prod. Discount Code");
                InvValueentry.SETRANGE("Global Dimension 1 Code", BranchRebate."Global Dimension 1 code");
                IF "Value Entry".GETFILTER("Item No.") <> '' THEN
                    InvValueentry.SETFILTER("Item No.", '%1', "Value Entry".GETFILTER("Item No."));
                InvValueentry.SETFILTER("Posting Date", '%1..%2', Startdate, enddate);
                InvValueentry.SETFILTER("Document Type", '<>%1', InvValueentry."Document Type"::"Sales Invoice");
                //InvValueentry.setfilter("Item Ledger Entry Quantity",'<%1',0);
                //InvValueentry.setfilter("Sales Amount (Actual)",'%1',0);
                IF InvValueentry.FINDSET THEN
                    REPEAT
                    BEGIN
                        //InvValueentry.CALCSUMS("Invoiced Quantity");
                        TotalSalesReturn += InvValueentry."Invoiced Quantity";
                    END;
                    UNTIL InvValueentry.NEXT = 0;
            END;
        end;*/

    procedure CheckIfDiscountAllowed(branchrebatecopy: Record "Branch Rebates Temp"): Boolean
    var
        itemSalesValuecopy: Decimal;
        Branchrebate3: Record "Branch Rebates Temp";
        itemSalesQuantitycopy: Decimal;
        "ItemDisc%copy": Decimal;
        DISC3: Record "Item Sales Disc. Qty. Lines";
    begin
        //IF Discountallowed THEN BEGIN
        itemSalesValuecopy := 0;
        itemSalesQuantitycopy := 0;
        "ItemDisc%copy" := 0;
        WITH branchrebatecopy DO BEGIN
            Branchrebate3.SETCURRENTKEY(
            "Source No.", "Prod. Discount Code", "Global Dimension 1 code", "Item No.", "Posting Date");
            Branchrebate3.SETRANGE("Source No.", "Source No.");
            Branchrebate3.SETRANGE("Prod. Discount Code", "Prod. Discount Code");
            Branchrebate3.SETFILTER("Posting Date", '%1..%2', Startdate, enddate);
            IF Branchrebate3.FINDSET THEN
                REPEAT
                    IF Branchrebate3."Sales Amount (Actual)" <> 0 THEN BEGIN
                        itemSalesValuecopy += Branchrebate3."Sales Amount (Actual)";
                        itemSalesQuantitycopy += (Branchrebate3."Item Ledg. Entry Qty(Discount)");
                    END;
                UNTIL Branchrebate3.NEXT = 0;

            DISC3.RESET;
            DISC3.SETRANGE(DISC3."Document No.", "Prod. Discount Code");
            DISC3.SETRANGE(DISC3."Branch Zone Code", ZoneCode);
            IF DISC3.FIND('-') THEN BEGIN
                REPEAT
                BEGIN
                    IF ABS((itemSalesQuantitycopy)) >= DISC3."Minimum Quantity" THEN BEGIN
                        "ItemDisc%copy" := DISC3."Discount %";
                    END;
                END
                UNTIL DISC3.NEXT = 0;
            END;
        END;
        IF "ItemDisc%copy" <> 0 THEN
            EXIT(TRUE);
    end;


    Procedure OnlineDiscExist(CustNo: Code[20]): Boolean
    var
        OnlineCustDisc: Record "Online Cust. Discount";
    Begin

        OnlineCustDisc.RESET;
        OnlineCustDisc.SETRANGE("Customer No.", CustNo);
        IF OnlineCustDisc.FINDSET THEN
            REPEAT
                IF (OnlineCustDisc."Start Date" <= enddate) AND
                   (OnlineCustDisc."End Date" >= enddate) THEN BEGIN
                    OnlineDiscPer := OnlineCustDisc."Discount %";
                    EXIT(TRUE);
                END;
            UNTIL OnlineCustDisc.NEXT = 0;
        EXIT(FALSE);
    End;

    procedure GetDims(Itemnrec: record Item; var Salin: Record "Sales Line");
    var
        DefaltDim: record "Default Dimension";
        DimMgmt: Codeunit DimensionManagement;
    begin
        TempDimSetEntry.DELETEALL();
        DefaltDim.Reset();
        DefaltDim.SetRange("Table ID", 27);
        DefaltDim.SetRange("No.", Itemnrec."No.");
        DefaltDim.SetFilter("Dimension Value Code", '<>%1', '');//PKON052421
        IF DefaltDim.FindSet() then
            repeat
                TempDimSetEntry.INIT;
                TempDimSetEntry.VALIDATE("Dimension Set ID", 0);
                TempDimSetEntry.VALIDATE("Dimension Code", DefaltDim."Dimension Code");
                TempDimSetEntry.VALIDATE("Dimension Value Code", DefaltDim."Dimension Value Code");
                TempDimSetEntry.INSERT(true);
            until DefaltDim.next = 0;
        Salin."Dimension Set ID" := DimMgmt.GetDimensionSetID(TempDimSetEntry);
        DimMgmt.UpdateGlobalDimFromDimSetID(Salin."Dimension Set ID", Salin."Shortcut Dimension 1 Code",
        Salin."Shortcut Dimension 2 Code");
    end;
}
