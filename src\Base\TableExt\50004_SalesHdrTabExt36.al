
tableextension 50004 SalesHdrTabExt36 extends "Sales Header"
{
    fields
    {
        field(50001; "Sales Type"; Enum SalesType)
        {
            DataClassification = CustomerContent;
        }
        field(50002; "Order Status"; enum OrderStatus)
        {
            DataClassification = CustomerContent;
            Editable = false;//PKON22AP8
        }
        field(50003; "Order Tracking"; Enum OrderTracking)
        {
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50005; "Reason Codes"; enum ReasonCodes)
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            begin
                // TestField("Reason Codes");
            end;
        }
        field(50006; "POS Window"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50007; Narration; Text[100])
        {
            DataClassification = CustomerContent;

            trigger OnValidate() // G2S CAS-01433-X1Y9V1-10092
            begin
                if "Narration" <> '' then
                    Rec."Posting Description" := Rec.Narration;
            end;
        }
        field(50055; "User Wise Locations"; code[20])
        {
            DataClassification = CustomerContent;

            trigger onlookup()
            var
                RespLoc: record "Responsibility Center Location";
                Loca: Record "location";
                CustShi: Record "Ship-to Address";
                //190424
                SalesHeader: Record "Sales Header";
            begin
                ////b2bpksalecorr11 entire triggers code changed.
                if "Sales Type" <> "Sales Type"::Direct then begin
                    //b2bpksalecorr9 Onlookup and Onvalidate Triggers code added
                    CustShi.Reset();
                    CustShi.SetRange("Customer No.", "Sell-to Customer No.");
                    CustShi.SetRange(Code, "Ship-to Code");
                    CustShi.Setfilter("Location Code", '<>%1', '');
                    IF CustShi.FindFirst() then
                        Error('Location is available in ship to code, you can not modify this field.');
                    RespLoc.Reset();
                    RespLoc.SetRange("Responsibility Center", "Responsibility Center");
                    if RespLoc.FindSet() then
                        IF page.RunModal(0, RespLoc) = ACTION::LookupOK then begin
                            clear("Location Code");
                            "User Wise Locations" := RespLoc."Location Code";
                            Validate("Location Code", "User Wise Locations");
                        end;
                end else
                    if "Sales Type" = "Sales Type"::Direct then begin
                        Loca.Reset();
                        Loca.setrange("Direct SO Location", True);
                        IF page.RunModal(0, Loca) = ACTION::LookupOK then begin
                            Loca.TestField(Blocked, false);//Balu 05122021<<
                            clear("Location Code");
                            "User Wise Locations" := Loca.Code;
                            Validate("Location Code", "User Wise Locations");
                        end;
                    end;



            end;

            trigger OnValidate()
            var
                RespLoc: record "Responsibility Center Location";
                CustShi: Record "Ship-to Address";
                Loca: Record Location;
            BEGIN
                //b2bpksalecorr9 trigger code added and field
                if "Sales Type" <> "Sales Type"::Direct then begin
                    CustShi.Reset();
                    CustShi.SetRange("Customer No.", "Sell-to Customer No.");
                    CustShi.SetRange(Code, "Ship-to Code");
                    CustShi.Setfilter("Location Code", '<>%1', '');
                    IF CustShi.FindFirst() then
                        Error('Location is available in ship to code, you can not modify this field.');
                    if "User Wise Locations" <> '' then begin
                        RespLoc.Reset();
                        RespLoc.SetRange("Responsibility Center", "Responsibility Center");
                        RespLoc.SetRange("Location Code", "User Wise Locations");
                        if NOT RespLoc.FindFIRST then
                            Error('Location code doesnt Exist For Responsibility centre Combination');
                    end else begin
                        clear("Location Code");
                        clear("User Wise Locations");
                    end;
                end else
                    if "Sales Type" = "Sales Type"::Direct then begin
                        Loca.Reset();
                        loca.setrange(code, "Location Code");
                        Loca.setrange("Direct SO Location", True);
                        IF Not Loca.findfirst then;//PK on 02.27.2021
                                                   //error('You can not select the location other than direct SO location.');//PK on 02.27.2021-Need to uncomment once open sales order upload.
                    end;
            end;
        }
        modify("Location Code")
        {
            trigger onaftervalidate()
            var
                LocationLrec: Record Location;
                SalesLineLRec: Record "Sales Line";
                Text001: Label 'Do you want to modify Sales Lines with Location Code?';
            begin
                IF "Location Code" <> '' THEN begin
                    LocationLrec.get("Location Code");
                    LocationLrec.TestField(Blocked, false);//Balu 05122021<<
                    CASE "Document Type" of
                        "Document Type"::Order, "Document Type"::Invoice:
                            begin
                                LocationLrec.TestField("Sal. Posted Shipment Nos.");
                                LocationLrec.TestField("Sal. Posted Invoice Nos.");
                                IF LocationLrec."Sal. Posted Shipment Nos." <> '' THEN
                                    "Shipping No. Series" := LocationLrec."Sal. Posted Shipment Nos.";
                                IF LocationLrec."Sal. Posted Invoice Nos." <> '' THEN
                                    "Posting No. Series" := LocationLrec."Sal. Posted Invoice Nos.";
                            end;
                        "Document Type"::"Return Order", "Document Type"::"Credit Memo":
                            begin
                                LocationLrec.TestField("Sal.Posted Return Receipt Nos.");
                                LocationLrec.TestField("Sal. Posted Credit Memo Nos.");
                                IF LocationLrec."Sal.Posted Return Receipt Nos." <> '' THEN
                                    "Return Receipt No. Series" := LocationLrec."Sal.Posted Return Receipt Nos.";
                                IF LocationLrec."Sal. Posted Credit Memo Nos." <> '' THEN
                                    "Posting No. Series" := LocationLrec."Sal. Posted Credit Memo Nos.";
                            end;
                    end;
                end;

                IF SalesLinesExist then begin
                    /*IF Not Confirm(Text001, false) then
                        Exit;*/
                    SalesLineLRec.Reset();
                    SalesLineLRec.SetRange("Document No.", "No.");
                    SalesLineLRec.SetRange("Document Type", "Document Type");
                    IF SalesLineLRec.FindSet() then
                        repeat
                            SalesLineLRec.Validate("Location Code", "Location Code");
                            SalesLineLRec.Modify();
                        until SalesLineLRec.Next = 0;
                end;
            end;
        }
        field(50004; "Loading Slip Required"; Boolean)
        {
            DataClassification = CustomerContent;
            //Editable = false;
        }
        /*field(50007; "Direct Sales Order"; Boolean)
        {
            DataClassification = CustomerContent;
        }*/
        field(50008; "Rebate Period Code"; Code[10])
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50009; Select; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50010; "Cr. Memo Sub Doc. Type"; Option)
        {
            DataClassification = CustomerContent;
            OptionCaption = '" ,Branch"';
            OptionMembers = " ",Branch;
        }
        field(50011; "Printable Comment 1"; Text[50])
        {
            DataClassification = CustomerContent;
            Description = 'Additional information to print on the Sales Order';

            trigger OnValidate()
            begin
                if Rec."Printable Comment 1" <> '' then Rec."Posting Description" := Rec."Printable Comment 1";
            end;
        }
        field(50012; "Cr. Memo Reason Type"; Option)
        {
            DataClassification = CustomerContent;
            OptionCaption = '" ,Return Order,Others,Canceled Invoice,Redistribution"';
            OptionMembers = " ","Return Order",Others,"Canceled Invoice",Redistribution;
            trigger OnValidate()
            begin
                // CHI1.0 >>
                IF "Cr. Memo Reason Type" <> xRec."Cr. Memo Reason Type" THEN BEGIN
                    IF SalesLinesExist THEN
                        ERROR(Text017, FIELDCAPTION("Cr. Memo Reason Type"));
                END;
                // CHI1.0 <<                 
            end;
        }
        field(50013; "Cr. Memo Stock Type"; Option)
        {
            DataClassification = CustomerContent;
            OptionCaption = '" ,Items,Non-Items"';
            OptionMembers = "",Items,"Non-Items";
        }
        field(50014; "Canceled Sales Inv. No."; Code[10])
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            begin
                TESTFIELD("Cr. Memo Reason Type", "Cr. Memo Reason Type"::"Canceled Invoice");
            end;
        }
        field(50015; "Printable Comment 2"; Text[50])
        {
            DataClassification = CustomerContent;
        }
        field(50016; "Release for Delivery"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50017; "Direct Sales Order"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50018; "GRN No."; Code[20])
        {
            DataClassification = CustomerContent;

        }
        field(50019; "Branch GRN No."; code[20])
        {
            DataClassification = CustomerContent;

        }

        field(50023; "Transporter Code"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Vendor."No.";

        }
        field(50024; "Transporter Name"; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50025; "Sales Ret. Created By"; code[50])
        {
            DataClassification = CustomerContent;
        }
        field(50026; "Customer Reference No."; Code[10])
        {
            DataClassification = CustomerContent;
        }
        field(50027; "Delivery Effected By"; Option)
        {
            OptionCaption = '" ,Customer,Marketing,"Supply Chain Department"';
            OptionMembers = " ",Customer,Marketing,"Supply Chain Department";
            DataClassification = CustomerContent;

        }

        field(50030; "Resp Wise Sell-to Customer No."; Code[20])
        {
            Caption = 'Resp Wise Sell-to Customer No.';
            // TableRelation = "Customer Resp. Cent. Lines"."Customer No." where("Resp. Center Code" = field("User Wise Resp Centr"));//b2bpksalecorr11
            DataClassification = CustomerContent;

            trigger OnLookup()
            var
                Cust: Record Customer;
                CustPage: Page "Customer List";
                CustPostGr: Record "Customer Posting Group";
                CompliGroup: Text[1000];
                CustRespCentForm: Page "Cust. Resp. Cent. Form";
                CustRespCentLines: Record "Customer Resp. Cent. Lines";
            BEGIN
                // >>>>>> G2S 12/12/2024 CAS-01380-T1P0C9
                IF REC."Sales Type" = Rec."Sales Type"::Complimentary THEN BEGIN
                    Clear(CompliGroup);
                    CustPostGr.SetRange(CHIERP_Complimentary, true);
                    IF CustPostGr.FindSet() THEN
                        REPEAT
                            IF CompliGroup = '' then
                                CompliGroup := CustPostGr.Code else
                                CompliGroup += '|' + CustPostGr.Code;
                        UNTIL CustPostGr.NEXT() = 0;
                    IF CompliGroup <> '' THEN BEGIN
                        Cust.Reset();
                        Cust.SetFilter("Responsibility Center", Rec."User Wise Resp Centr");
                        Cust.SetFilter("Customer Posting Group", CompliGroup);
                        IF Cust.FindSet() THEN BEGIN
                            // CustPage.SetTableView(Cust)
                            IF PAGE.RunModal(PAGE::"Customer List", Cust) = ACTION::LookupOK THEN BEGIN
                                Rec.Validate("Resp Wise Sell-to Customer No.", Cust."No.");
                            END
                        END Else
                            Message('No Customer found...');
                    END ELSE
                        ERROR('No Complimentary Customer Found');
                END ELSE BEGIN
                    CustRespCentLines.SetCurrentKey("Customer No.");
                    CustRespCentLines.SetRange("Resp. Center Code", Rec."User Wise Resp Centr");
                    IF CustRespCentLines.FindSet() THEN BEGIN
                        // CustRespCentForm.SetTableView(CustRespCentLines);
                        IF PAGE.RunModal(PAGE::"Cust. Resp. Cent. Form", CustRespCentLines) = ACTION::LookupOK THEN BEGIN
                            Rec.Validate("Resp Wise Sell-to Customer No.", CustRespCentLines."Customer No.");
                        END
                    END Else
                        Message('No Customer found...');

                END;
            END;
            // >>>>>> G2S 12/12/2024 CAS-01380-T1P0C9

            trigger OnValidate()
            var
                myInt: Integer;
                fixedrebates: Record "Fixed Rebate";
                custrecrebate: Record customer;
            begin
                Validate("Sell-to Customer No.", "Resp Wise Sell-to Customer No.");
                //g2s 260424
                Rec.Testfield("Sell-to Customer No.");
                Rec.TestField("Order Date");
                Rec."Initial Rebate Disc. Calc" := Rec.pullDiscount(Rec);
                //rebate issues
                if custrecrebate.get("Resp Wise Sell-to Customer No.") then begin
                    if custrecrebate."Fixed Rebate Code" <> '' then begin
                        rec.Validate("Fixed Rebate Code", custrecrebate."Fixed Rebate Code");
                    end;
                end;
                Rec.Modify();
                UpdateFixedDiscount();

            end;
        }
        /*  field(50031; "Processed by API"; Boolean)
         {
             DataClassification = CustomerContent;
             Description = 'Processed by API';
             Editable = false;
         }
         field(50032; "Processed by API On"; DateTime)
         {
             DataClassification = CustomerContent;
             Description = 'Processed by API On';
             Editable = false;
         } */
        field(50045; "Posted Loading Slip No."; code[20])
        {
            DataClassification = CustomerContent;
            //Editable = false;
            TableRelation = "Posted Loading SLip Header";
            //B2B.P.K.T
        }
        field(50061; "Pos Load Slip Reason Code"; enum PLSPReasonCode)
        {
            DataClassification = CustomerContent;

        }
        field(50062; "User Wise Resp Centr"; Code[20])
        {
            DataClassification = CustomerContent;
            trigger OnLookup()
            var
                respAll: record "Responsibility Center";
            begin
                UserCentrRespLRec.Reset();
                UserCentrRespLRec.SetRange("User ID", UserId);
                if UserCentrRespLRec.FindSet() then
                    IF page.RunModal(50406, UserCentrRespLRec) = ACTION::LookupOK then begin
                        Clear("Resp Wise Sell-to Customer No.");//b2bpksalecorr9
                        Clear("Sell-to Customer No.");//b2bpksalecorr9
                        "User Wise Resp Centr" := UserCentrRespLRec."Resp. Center Code";
                        Validate("Responsibility Center", "User Wise Resp Centr");
                    end;
                if NOT UserCentrRespLRec.FindSet() then begin
                    respAll.RESET;
                    IF respAll.FINDSET THEN
                        IF page.RunModal(0, respAll) = ACTION::LookupOK then begin
                            Clear("Resp Wise Sell-to Customer No.");//b2bpksalecorr9
                            Clear("Sell-to Customer No.");//b2bpksalecorr9
                            "User Wise Resp Centr" := respAll.code;
                            Validate("Responsibility Center", "User Wise Resp Centr");
                        end;
                end
            end;

            trigger OnValidate()
            begin
                if "User Wise Resp Centr" <> '' then begin
                    clear("Resp Wise Sell-to Customer No.");//b2bpksalecorr9
                    Clear("Sell-to Customer No.");//b2bpksalecorr9
                    UserCentrRespLRec2.Reset();
                    UserCentrRespLRec2.SetRange("User ID", UserId);
                    if UserCentrRespLRec2.FindFIRST then begin
                        UserCentrRespLRec.Reset();
                        UserCentrRespLRec.SetRange("User ID", UserId);
                        UserCentrRespLRec.SetRange("Resp. Center Code", "User Wise Resp Centr");
                        if NOT UserCentrRespLRec.Findfirst() then
                            Error('Responsibility Centre doesnt Exist For User Combination');
                    end;
                end else begin
                    //Validate("Responsibility Center", '');
                    clear("Responsibility Center");//b2bpksalecorr9
                    Clear("Resp Wise Sell-to Customer No.");//b2bpksalecorr9
                    Clear("Sell-to Customer No.");//b2bpksalecorr9
                end;
            end;

        }
        //Fix06Jul2021>>
        field(50064; "Sales Area"; Code[20])
        {
            DataClassification = CustomerContent;
            Editable = false;
            Caption = 'Sales Office';//PKONJU7
        }
        //Fix06Jul2021<<
        field(65000; Total; Decimal)
        {
            Description = 'GJ_CHIPOS_RKD_141013';
            FieldClass = FlowField;
            CalcFormula = Sum("Sales Line"."Amount Including VAT" WHERE("Document Type" = FIELD("Document Type"), "Document No." = FIELD("No.")));
        }

        field(65004; "POS Transaction Type"; Option)
        {
            Description = 'GJ_CHIPOS_RKD_181113';
            OptionCaption = ',Cash,Card,Both';//,Fund Transfer';//CR220067-PKON22M23
            OptionMembers = ,Cash,Card,Both;//,"Fund Transfer";//CR220067-PKON22M23
        }
        field(65005; "POS Transaction No."; Code[18])
        {
            Description = 'GJ_CHIPOS_RKD_181113';
            //B2BPKON21NOv2022>>
            trigger OnValidate()
            var
                Salesinvoiceheader: record "Sales Invoice Header";
            begin
                if "POS Transaction No." = '' then
                    exit;
                Salesinvoiceheader.Reset();
                ;
                Salesinvoiceheader.SetRange("POS Transaction No.", "POS Transaction No.");
                if Salesinvoiceheader.FindFirst() then
                    Error('POs Transaction No already used');


            end;
            //B2BPKON21NOv2022<<
        }
        field(65006; "User ID"; Code[50])
        {
            Description = 'GJ_CHIPOS_RKD_181113,CHIUPG';
        }
        field(65007; "POS Card Amount"; Decimal)
        {
        }
        field(65008; "POS Cash Amount"; Decimal)
        {
        }
        field(65009; "Pos Bank Name"; Option)
        {
            //OptionCaption = '" ,ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB"';//PKONDE16
            //OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB;//PKONDE16
            OptionCaption = '" ,ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB",CORONATION,Zenith Fund Transfer,Access Fund Transfer,PROVIDUS,GTB Fund Transfer';//PKONDE16  //RFC#40 // G2S 8300_CAS-01400-R6C8L3
            OptionMembers = " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,FCMB,CORONATION,"Zenith Fund Transfer","Access Fund Transfer",PROVIDUS,"GTB Fund Transfer";//PKONDE16 //RFC#40 // G2S 8300_CAS-01400-R6C8L3

            trigger OnValidate();
            begin
                case "Pos Bank Name" of
                    "Pos Bank Name"::" ":
                        "POS Account No." := '';
                    "Pos Bank Name"::ZB:
                        "POS Account No." := 'ZIL013';
                    "Pos Bank Name"::GTB:
                        "POS Account No." := 'GTB0033';
                    "Pos Bank Name"::UBA:
                        "POS Account No." := 'UBA0035';
                    "Pos Bank Name"::STANBIC:
                        "POS Account No." := 'SBIC003';
                    "Pos Bank Name"::ACCESS:
                        "POS Account No." := 'ACC009';
                    "Pos Bank Name"::"Zenith Fund Transfer":  //RFC#40
                        "POS Account No." := 'ZIL164';
                    "Pos Bank Name"::"Access Fund Transfer": //RFC#40
                        "POS Account No." := 'ACC006';       //RFC#40
                    "Pos Bank Name"::"GTB Fund Transfer": //G2S 8300_CAS-01400-R6C8L3
                        "POS Account No." := 'GTB0033';

                end;
            end;
        }
        field(65011; "POS Account No."; Code[10])
        {

            trigger OnValidate();
            begin
                //  added to re confirm POS bank name SAA3.0 30/03/2017
                if not CONFIRM(Text50222, false, "Pos Bank Name") then begin
                    "Pos Bank Name" := 0;
                    MESSAGE(Text50221);
                end;
                // end;
            end;
        }
        //B2BMS
        field(65012; "Created By"; Text[50])
        {
            Editable = false;
        }
        field(65013; "Created Date"; DateTime)
        {
            Editable = false;
        }
        field(65014; "Modified By"; Text[50])
        {
            Editable = false;
        }
        field(65015; "Modified date"; DateTime)
        {
            Editable = false;
        }

        //B2BMS
        //B2BBaluonJan31 2022>>
        field(65016; "Final Approval"; Option)
        {
            CalcFormula = Lookup("Approval Entry".Status WHERE("Document No." = FIELD("No.")));
            Editable = false;
            FieldClass = FlowField;
            OptionCaption = 'Created, Open, Canceled, Rejected, Approved';
            OptionMembers = Created,Open,Canceled,Rejected,Approved;
        }
        field(65017; "Approved Date Time"; DateTime)
        {
            CalcFormula = Lookup("Approval Entry"."Last Date-Time Modified" WHERE("Document No." = FIELD("No.")));
            Editable = false;
            FieldClass = FlowField;
        }
        field(65018; "Final Approval ID"; code[50])
        {
            CalcFormula = lookup("Approval Entry"."Last Modified By User ID" WHERE("Document No." = FIELD("No.")));
            Editable = false;
            FieldClass = FlowField;

        }
        field(65019; "Applied By"; code[50]) //PKON22AP28.2
        {
            Editable = false;
            DataClassification = CustomerContent;

        }

        //B2BBaluonJan31 2022<<
        field(65027; "Released By"; Code[50])
        {
            DataClassification = ToBeClassified;

        }
        //B2BSPON22OCT18
        //G2sAdded 270324

        field(65028; "Rebate Discount"; Decimal)
        {
            FieldClass = FlowField;
            CalcFormula = Sum("Sales Line"."Rebate Discount" WHERE("Document Type" = FIELD("Document Type"), "Document No." = FIELD("No.")));
        }
        field(65029; "Inv. Disc. Calc on Rebate"; Boolean)
        {
            DataClassification = ToBeClassified;

        }

        field(65030; "Inv. Disc. Amt Calc on Rebate"; Decimal)
        {
            // DataClassification = ToBeClassified;
            FieldClass = FlowField;
            CalcFormula = Sum("Sales Line"."rebate Disc. Amount to Inv." WHERE("Document Type" = FIELD("Document Type"), "Document No." = FIELD("No.")));

        }
        field(65031; "Initial Rebate Disc. Calc"; Decimal)
        {
            DataClassification = ToBeClassified;
        }
        //G2S 270324
        //rebate issue >>>
        field(65032; "Fixed Rebate Code"; code[20])
        {
            DataClassification = ToBeClassified;
            trigger OnValidate()
            var
                FixedRebates: Record "Fixed Rebate";
            begin
                if "Fixed Rebate Code" <> '' then begin
                    if FixedRebates.Get("Fixed Rebate Code") then
                        Validate("Fixed Rebate", FixedRebates."Fixed Rebate");
                end else
                    Validate("Fixed Rebate", 0);
            end;
        }
        field(65033; "Fixed Rebate"; Decimal)
        {
            DataClassification = ToBeClassified;
            Editable = false;
        }
        field(65034; "Fixed Rebate Amount"; Decimal)
        {
            // DataClassification = ToBeClassified;
            FieldClass = FlowField;
            CalcFormula = Sum("Sales Line"."Fixed Rebate Amount" WHERE("Document Type" = FIELD("Document Type"), "Document No." = FIELD("No.")));

        }
        field(65035; "Fixed Rebate Amount to Inv."; Decimal)
        {
            FieldClass = FlowField;
            CalcFormula = Sum("Sales Line"."Fixed Rebate Amount to Inv." WHERE("Document Type" = FIELD("Document Type"), "Document No." = FIELD("No.")));

        }
        field(65100; "POS Bank Names"; Text[200])
        {
            DataClassification = ToBeClassified;
            Editable = false;

        }
        // <<<





        modify("Sell-to Customer No.")
        {
            trigger OnAfterValidate()
            var
                CustCrLimitSchdLRec: Record "Cust. Cr. Limit Schedule";
                CustCheck: Record Customer;
                CustRespCentLines: Record "Customer Resp. Cent. Lines";
            BEGIN
                VALIDATE("Responsibility Center", "User Wise Resp Centr"); //b2bpksalecorr10
                IF (CustGRec.get("Sell-to Customer No.")) THEN begin
                    CustGRec.TestField("Approval Status", CustGRec."Approval Status"::Released);
                    "Sales Area" := CustGRec."Sales Office";//Fix06Jul2021//PKONJU7
                    if ("Document Type" = "Document Type"::Quote) OR ("Document Type" = "Document Type"::Order) then begin
                        IF CustGRec."Gen. Bus. Posting Group" = 'SCRAP' then
                            "Loading Slip Required" := false
                        else
                            "Loading Slip Required" := true;
                        IF Rec."Sales Type" = Rec."Sales Type"::Complimentary THEN
                            "Loading Slip Required" := false
                    end
                end;

                //>>>> G2S 08/04/2024 CAS-01414-Q6Q7Q8
                IF REC."Document Type" = Rec."Document Type"::"Return Order" THEN BEGIN
                    CustRespCentLines.SetRange("Customer No.", "Sell-to Customer No.");
                    IF CustRespCentLines.FindFirst() THEN
                        Rec."Responsibility Center" := CustRespCentLines."Resp. Center Code";
                END;
                //>>>> G2S 08/04/2024 CAS-01414-Q6Q7Q8

                CustCheck.GET("Sell-to Customer No.");
                if not "POS Window" then
                    IF ("Document Type" = "Document Type"::Order) or ("Document Type" = "Document Type"::Quote) Then Begin
                        CustCrLimitSchdLRec.SETFILTER("Customer No.", "Sell-to Customer No.");
                        CustCrLimitSchdLRec.SETRANGE("Responsibility Center", "User Wise Resp Centr");//b2bpksalecorr10
                        IF CustCheck."Customer Credit type" = CustCheck."Customer Credit type"::"Cash n Carry" THEN
                            CustCrLimitSchdLRec.SETFILTER("Customer Type", '%1', CustCrLimitSchdLRec."Customer Type"::"Cash Customer")
                        else
                            IF (CustCheck."Customer Credit type" = CustCheck."Customer Credit type"::General) OR (CustCheck."Customer Credit type" = CustCheck."Customer Credit type"::Central) THEN bEGIN
                                CustCrLimitSchdLRec.SETFILTER("Customer Type", '%1', CustCrLimitSchdLRec."Customer Type"::"Credit Customer");
                                CustCrLimitSchdLRec.SETFILTER("Schedule Limit Expiry Date", '>=%1', TODAY);
                            end;
                        CustCrLimitSchdLRec.SETRANGE(Status, CustCrLimitSchdLRec.Status::Released);
                        IF NOT CustCrLimitSchdLRec.FindFirst() then
                            Error('Cash or Credit limit setup is not available for this customer Resp %1 cust %2 and Custcredit Type %3',
                            "User Wise Resp Centr", "Sell-to Customer No.", CustCheck."Customer Credit type"); //b2bpksalecorr10
                    end;
            END;

        }
        modify("Bill-to Customer No.")
        {
            trigger OnAfterValidate()
            var
                SalRecSetup: Record "Sales & Receivables Setup";
                CustCrLimitSchdGRec: Record "Cust. Cr. Limit Schedule";
                Text50200: Label 'Credit Limit schedule Line doesnt exists for this Combination Customer-%1  , Responsibility Center-%2 and Shortcut Dimension-%3.';
            BEGIN
                /*//b2bpksalecorr10
                IF (CustGRec.get("Bill-to Customer No.")) THEN
                    CustGRec.TestField("Approval Status", CustGRec."Approval Status"::Released);

                IF SalRecSetup.GET THEN;
                IF SalRecSetup."Validate Cust. Credit/Cash" THEN BEGIN
                    IF NOT ("Document Type" IN ["Document Type"::"Credit Memo", "Document Type"::"Return Order", "Document Type"::Quote]) THEN BEGIN
                        CustCrLimitSchdGRec.RESET;
                        CustCrLimitSchdGRec.SETRANGE("Customer No.", "Bill-to Customer No.");
                        CustCrLimitSchdGRec.SETRANGE("Shortcut Dimension 1 Code", "Shortcut Dimension 1 Code");
                        CustCrLimitSchdGRec.SETRANGE("Responsibility Center", "Responsibility Center");
                        CustCrLimitSchdGRec.SETRANGE(Status, CustCrLimitSchdGRec.Status::Released);
                        IF NOT CustCrLimitSchdGRec.FINDLAST THEN
                            ERROR(Text50200, "Bill-to Customer No.", "Responsibility Center", "Shortcut Dimension 1 Code");
                        CheckBalanceAvailable;
                    END;
                END;*///b2bpksalecorr10
            END;
        }
        modify("Your Reference")
        {
            trigger OnAfterValidate()
            begin
                TESTFIELD("Cr. Memo Reason Type", "Cr. Memo Reason Type"::"Return Order");
            end;
        }
        modify("Applies-to ID")
        {
            trigger OnAfterValidate()
            BEGIN
                //PKON22AP28.2>>
                Clear("Applied By");
                IF "Applies-to ID" <> '' then BEGIN
                    "Applied By" := UserId;
                    "Applies-to ID" := "No.";
                end ELSE Begin
                    Clear("Applied By");
                    Clear("Applies-to ID");
                End;
                //PKON22AP28.2<<
            END;
        }
        modify("Ship-to Code")
        {
            trigger OnAfterValidate()
            var
                CustShi: Record "Ship-to Address";
            BEGIN
                //b2bpksalecorr9 trigger code added and field
                IF not "POS Window" then begin
                    CustShi.Reset();
                    CustShi.SetRange("Customer No.", "Sell-to Customer No.");
                    CustShi.SetRange(Code, "Ship-to Code");
                    CustShi.Setfilter("Location Code", '<>%1', '');
                    IF CustShi.FindFirst() then begin
                        Validate("Location Code", CustShi."Location Code");
                        "User Wise Locations" := CustShi."Location Code";
                    end else begin
                        CLEAR("Location Code");
                        CLEAR("User Wise Locations");
                    end;
                end;
            end;
        }
        //190424
        modify("Order Date")
        {
            trigger OnAfterValidate()
            var
                SalesHead: Record "Sales Header";
            begin
                Rec.Testfield("Sell-to Customer No.");
                Rec."Initial Rebate Disc. Calc" := Rec.pullDiscount(Rec);
                Rec.Modify();
                //rec.Validate("Invoice Discount Value");
            end;
        }
        modify("Sell-to Customer Name")
        {
            trigger OnAfterValidate()
            var
                CustRespCentLines: Record "Customer Resp. Cent. Lines";
            begin
                //>>>> G2S 08/04/2024 CAS-01414-Q6Q7Q8
                IF REC."Document Type" = Rec."Document Type"::"Return Order" THEN BEGIN
                    CustRespCentLines.SetRange("Customer No.", "Sell-to Customer No.");
                    IF CustRespCentLines.FindFirst() THEN
                        Rec."Responsibility Center" := CustRespCentLines."Resp. Center Code";
                END;
                //>>>> G2S 08/04/2024 CAS-01414-Q6Q7Q8
            end;
        }

    }
    trigger OnAfterModify()
    Var
        CustGMRec: record customer;
        LocGMrec: Record Location;
    begin
        if "Order Status" = "Order Status"::"Short Closed" then
            Error('Order is short closed ');
        //message('Mail Sending Checking for Customer and LSocation %1..', Status);
        IF ("Document Type" = "Document Type"::Order) And (Rec.Status = rec.status::Released) then begin
            IF CustGMRec.get("Sell-to Customer No.") AND (CustGMRec."E-Mail" <> '') then
                SendReleaseMails(CustGMRec."E-Mail", 0);//b2bpksalecorr10
            IF LocGMrec.get("Location Code") AND (LocGMrec."E-Mail" <> '') then
                SendReleaseMails(LocGMrec."E-Mail", 1);//b2bpksalecorr10
        end
    end;

    trigger OnAfterInsert()
    var
        UserSetup: Record "User Setup";
        CReCeL: Record "Customer Resp. Cent. Lines";
        CustRec: Record Customer;
        POSSetup: Record "UserID Resp. Cent. Lines";
        POStrans: record "Retail Sales Transaction Log";
    BEGIN
        //"Loading Slip Required" := true;
        IF "POS Window" THEN BEGIN
            //"Posting Date" := WORKDATE;
            POStrans.SetRange("Transaction ID", rec."External Document No.");
            if POStrans.IsEmpty then begin
                UserSetup.RESET;
                UserSetup.SETRANGE("User ID", USERID);
                IF UserSetup.FINDFIRST THEN;
                IF "No." <> '' THEN BEGIN
                    "User Wise Resp Centr" := UserSetup."Sales Resp. Ctr. Filter";
                    //VALIDATE("Responsibility Center", UserSetup."Sales Resp. Ctr. Filter");
                    CReCeL.RESET;
                    CReCeL.SETRANGE("Resp. Center Code", UserSetup."Sales Resp. Ctr. Filter");
                    //CReCeL.SETRANGE("Customer No.",CustRec."No.");
                    IF CReCeL.FINDFIRST THEN begin
                        REPEAT
                            CustRec.RESET;
                            CustRec.SETRANGE("No.", CReCeL."Customer No.");
                            CustRec.SETRANGE("POS Customer", TRUE);
                            IF CustRec.FINDFIRST THEN BEGIN
                                VALIDATE("Sell-to Customer No.", CReCeL."Customer No.");
                            END;

                        UNTIL (CReCeL.NEXT = 0) or ("Sell-to Customer No." <> '');
                        VALIDATE("Location Code", UserSetup.Location);
                        IF POSSetup.GET(UserSetup.Location) THEN;
                        "Posting Date" := Today;
                        //VALIDATE("Responsibility Center",POSSetup."Resp. Center Code");
                        VALIDATE("Salesperson Code", UserSetup."Salespers./Purch. Code");
                        VALIDATE("Ship-to Code", '1');
                        Modify();
                        //Message('Customer order %1 is created', "No.");
                    END;
                end;
            END;
        end;
    END;
    //B2BMS
    trigger OnBeforeDelete()
    var
        salesHdr: Record "Sales Header";
    begin
        // salesHdr.SetRange("No.", rec."Document No.");
        //if salesHdr.FindFirst() then begin
        if Rec.RebateVariableexits() then
            Rec.ReturnRebateBalance();
    end;


    trigger OnDelete()
    begin
        // <<<<<< G2S CAS-01322-K9V3S6 7/18/2024
        ValidateDocAccess(Rec."Sales Type");
        // >>>>>> G2S CAS-01322-K9V3S6 7/18/2024
    end;

    trigger OnInsert()
    begin
        "Created By" := UserId;
        "Created Date" := CreateDateTime(WorkDate, Time);
        // <<<<<< G2S CAS-01322-K9V3S6 7/18/2024
        // ValidateDocAccess(Rec."Sales Type");
        // >>>>>> G2S CAS-01322-K9V3S6 7/18/2024
    end;

    trigger OnModify()
    begin
        "Modified By" := UserId;
        "Modified date" := CreateDateTime(WorkDate, Time);
        // <<<<<< G2S CAS-01322-K9V3S6 7/18/2024
        // ValidateDocAccess(Rec."Sales Type");
        // >>>>>> G2S CAS-01322-K9V3S6 7/18/2024
    end;
    //B2BMS

    // <<<<<< G2S CAS-01322-K9V3S6 7/18/2024
    procedure ValidateDocAccess(var docType: Enum SalesType)
    var
        OrderErrorMsg: Label 'You dont have Access to this Page or Document./ Contact Administrator';
        userSetup: Record "User Setup";
    begin
        userSetup.SetRange("User ID", UserId);
        if userSetup.FindFirst() then begin
            case docType of
                docType::Local:
                    begin
                        if userSetup."Can Access Local Sales Doc" then Error(OrderErrorMsg);
                    end;

                docType::Direct:
                    begin
                        if userSetup."Can Access Direct Sales Doc" then Error(OrderErrorMsg);
                    end;

                docType::Export:
                    begin
                        if userSetup."Can Access Export Sales Doc" then Error(OrderErrorMsg);
                    end;
            end;
        end else
            Error('User does not exist');
    end;

    procedure ConfirmSalesType(): Boolean
    begin
        if Rec."Sales Type" in [Rec."Sales Type"::Direct, Rec."Sales Type"::Export, Rec."Sales Type"::Local] then exit(true) else exit(false);
    end;
    // >>>>>> G2S CAS-01322-K9V3S6 7/18/2024

    procedure CheckBalanceAvailable()
    var
        CustCrLimitSchdLRec: record "Cust. Cr. Limit Schedule";
        CustomerEntry: record "Cust. Ledger Entry";
        SalesLineAmountTotal: Decimal;
        SalesLineRec: Record "Sales Line";
        Amt: Decimal;
        Text50000: Label 'The Balance %1 has been exhausted by pending order Totals of %2';
    BEGIN

        CustCrLimitSchdLRec.RESET;
        CustCrLimitSchdLRec.SETRANGE("Customer No.", "Sell-to Customer No.");
        CustCrLimitSchdLRec.SETRANGE("Responsibility Center", "Responsibility Center");
        CustCrLimitSchdLRec.SETRANGE(Status, CustCrLimitSchdLRec.Status::Released);
        CustCrLimitSchdLRec.SETFILTER(CustCrLimitSchdLRec."Customer Type", '%1', CustCrLimitSchdLRec."Customer Type"::"Credit Customer");
        IF CustCrLimitSchdLRec.ISEMPTY THEN BEGIN
            CustCrLimitSchdLRec.RESET;
            CustCrLimitSchdLRec.SETRANGE("Customer No.", "Sell-to Customer No.");
            CustCrLimitSchdLRec.SETRANGE("Responsibility Center", "Responsibility Center");
            CustCrLimitSchdLRec.SETRANGE(Status, CustCrLimitSchdLRec.Status::Released);
            CustCrLimitSchdLRec.SETFILTER(CustCrLimitSchdLRec."Customer Type", '%1', CustCrLimitSchdLRec."Customer Type"::"Cash Customer");
            IF CustCrLimitSchdLRec.FINDLAST THEN BEGIN
                CustomerEntry.SETCURRENTKEY("Customer No.", "Applies-to ID");
                Amt := 0;
                CustomerEntry.RESET;
                CustomerEntry.SETRANGE("Customer No.", "Sell-to Customer No.");
                CustomerEntry.SETRANGE("Responsibility Center", "Responsibility Center");
                CustomerEntry.SETFILTER(Open, '%1', TRUE);

                CustomerEntry.FINDSET;
                REPEAT
                    CustomerEntry.CALCFIELDS(CustomerEntry."Remaining Amount");
                    Amt := Amt + CustomerEntry."Remaining Amount";
                UNTIL CustomerEntry.NEXT = 0;

                IF "Document Type" = "Document Type"::Order THEN BEGIN
                    SalesLineAmountTotal := 0;
                    SalesLineRec.SETCURRENTKEY("Document Type", "Sell-to Customer No.", "Responsibility Center");
                    SalesLineRec.SETRANGE("Document Type", SalesLineRec."Document Type"::Order);
                    SalesLineRec.SETRANGE("Sell-to Customer No.", "Sell-to Customer No.");
                    SalesLineRec.SETRANGE("Responsibility Center", "Responsibility Center");
                    IF SalesLineRec.FINDSET THEN
                        REPEAT
                            SalesLineAmountTotal += SalesLineRec."Outstanding Amount";
                        UNTIL SalesLineRec.NEXT = 0;
                END;
                IF (Amt - SalesLineAmountTotal) > 0 THEN
                    ERROR(Text50000, Amt, SalesLineAmountTotal);
            END;
        END;
    END;

    // g2s PullDiscount 100224
    Procedure pullDiscount(var recSalesHeader: Record "Sales Header"): Decimal
    var
        rebateRecordTab: Record "Rebate Records";
        rebatePeriodCodes: Record "Rebate Period Codes";
        salesHeaderRec: Record "Sales header";
        kdCustFocus: Record "KD Cust. Focus Brands ";
        MonthlyRebateSetup: Record "Monthly Rebate Setup";
        startDate: Code[20];
        RebateDiscount: Decimal;
        invdate: Date;
        startdates: Date;
        enddates: Date;
    begin
        salesHeaderRec := recSalesHeader;
        kdCustFocus.Setrange("Customer No.", recSalesHeader."Bill-to Customer No.");
        kdcustfocus.SETFILTER("Customer Type", '%1|%2|%3', kdCustfocus."Customer Type"::KD,
                                            kdcustfocus."Customer Type"::"WD-L1",
                                            kdcustfocus."Customer Type"::"WD-L2");//Added to filter the KDcustomer

        if kdCustFocus.FindFirst() then begin
            invdate := WORKDATE;
            startdates := CALCDATE('+1M', invdate);
            enddates := CALCDATE('+2M', invdate);
            RebateDiscount := 0;
            // rebatePeriodCodes.Reset();
            // rebatePeriodCodes.Setfilter("start Date", '<=%1', recSalesHeader."Document Date");
            // rebatePeriodCodes.Setfilter("end Date", '>=%1', recSalesHeader."Document Date");
            // if rebatePeriodCodes.FindFirst() then begin
            rebateRecordTab.Reset();
            rebateRecordTab.SetCurrentKey("Rebate Period", "Customer No.");
            rebateRecordTab.setrange(rebateRecordTab."Customer No.", recSalesHeader."Bill-to Customer No.");
            //rebateRecordTab.SetFilter(rebateRecordTab."Posting Date", '<=%1', recSalesHeader."Posting Date");
            rebateRecordTab.SetFilter(rebateRecordTab."Posting Date", '<=%1', recSalesHeader."Order Date");
            //210324
            rebateRecordTab.SetFilter("Rebate Amount", '<>%1', 0);
            rebateRecordTab.Setfilter("Rebate Discount Status", '%1..%2', rebateRecordTab."Rebate Discount Status"::Initial, rebateRecordTab."Rebate Discount Status"::Partial);
            rebateRecordTab.CalcSums(rebateRecordTab."Balance Rebate Amount");
            //210324
            // if rebateRecordTab.FindFirst() then begin
            //     MonthlyRebateSetup.FindFirst();

            //     if (rebateRecordTab."Rebate Amount" <> 0) then begin
            //         RebateDiscount := rebateRecordTab."Rebate Amount" * (MonthlyRebateSetup."Discount in %" / 100);
            //         RebateDiscount := Round(RebateDiscount, 0.01, '<');
            //         Exit(RebateDiscount);
            //     end;
            exit(rebateRecordTab."Balance Rebate Amount");
            // end;

        end;


    end;

    Procedure pullDiscount2(var recSalesHeader: Record "Sales Header"): Decimal
    var
        rebateRecordTab: Record "Rebate Records";
        rebatePeriodCodes: Record "Rebate Period Codes";
        salesHeaderRec: Record "Sales header";
        kdCustFocus: Record "KD Cust. Focus Brands ";
        MonthlyRebateSetup: Record "Monthly Rebate Setup";
        startDate: Code[20];
        RebateDiscount: Decimal;
        SalesLine: Record "Sales Line";
    begin
        RebateDiscount := 0;
        salesHeaderRec := recSalesHeader;
        kdCustFocus.Setrange("Customer No.", recSalesHeader."Bill-to Customer No.");
        kdcustfocus.SETFILTER("Customer Type", '%1|%2|%3', kdCustfocus."Customer Type"::KD,
                                            kdcustfocus."Customer Type"::"WD-L1",
                                            kdcustfocus."Customer Type"::"WD-L2");//Added to filter the KDcustomer

        if kdCustFocus.FindFirst() then begin
            RebateDiscount := 0;
            rebatePeriodCodes.Reset();
            rebatePeriodCodes.Setfilter("start Date", '<=%1', recSalesHeader."Document Date");
            rebatePeriodCodes.Setfilter("end Date", '>=%1', recSalesHeader."Document Date");
            if rebatePeriodCodes.FindFirst() then begin
                rebateRecordTab.Reset();
                rebateRecordTab.setrange("Customer No.", recSalesHeader."Bill-to Customer No.");
                rebateRecordTab.SetRange("Rebate Period", rebatePeriodCodes.Code);
                if rebateRecordTab.FindFirst() then begin
                    MonthlyRebateSetup.FindFirst();
                    // then
                    // begin
                    //if (rebateRecordTab."No. of Transaction in a Month" <= MonthlyRebateSetup."Count in Months") then begin
                    // RebateDiscount := rebateRecordTab."Rebate Amount" * (MonthlyRebateSetup."Discount in %" / 100);
                    // RebateDiscount := Round(RebateDiscount, 0.01, '<');
                    // rebateRecordTab."No. of Transaction in a month" += 1;
                    SalesLine.Reset();
                    SalesLine.SetRange("Document No.", recSalesHeader."No.");
                    SalesLine.CalcSums("Line Amount");
                    RebateDiscount := (MonthlyRebateSetup."Discount in %" / 100) * SalesLine."Line Amount";
                    if rebateRecordTab."Balance Rebate Amount" > RebateDiscount then begin
                        rebateRecordTab."Balance rebate Amount" -= rebateRecordTab."Rebate Amount";
                        rebateRecordTab."Rebate Amount" := rebateRecordTab."Balance Rebate Amount";
                        rebateRecordTab."No. of Transaction in a month" += 1;
                        rebateRecordTab.modify();
                    end;
                    Exit(RebateDiscount);
                    // end;
                    //end;
                end;
            end;

        end;


    end;


    procedure UpdateFixedDiscount()
    var
        salesLRec: record "Sales Line";
    begin
        SalesLRec.Reset();
        SalesLRec.SetRange("Document Type", "Document Type");
        SalesLRec.SetRange("Document No.", "No.");
        IF SalesLRec.FindFirst() then
            repeat

                if "Fixed Rebate Code" <> '' then begin
                    salesLRec.validate(Quantity);
                    salesLRec.modify();
                end;

            until salesLRec.Next() = 0;
    end;

    procedure CheckSalesMandValues(CheckStock: Boolean)//PKON22AP28.2-CR220058

    var
        CustCrLimitSchdLRec: Record "Cust. Cr. Limit Schedule";
        CustCheck: Record Customer;
        CustLed: record "Cust. Ledger Entry";
        CustLed2: record "Cust. Ledger Entry";
        Text0002: Label 'Apply the document in the Apply entries with the specified Applies to ID.';
        Text0001: Label 'The  %1 or %2 Must not be Blank for a cash Customer';
        SalRecSetup: Record "Sales & Receivables Setup";
        AddCuCrLimitGRec: Record "Add. Cust. Cr. Limit Schedule";
        CustLdgLRec: Record "Cust. Ledger Entry";
        Amt: Decimal;
        Text0003: Label 'Credit Limit Exceeds Credit Limit Available %1 and Sales Order Value %2';
        SalesLinRec: Record "Sales Line";
        InvoicAmnt: Decimal;
        SalesLineAmountTotal: Decimal;
        OutSalesLineAmountTotal: Decimal;
        SalesLineRec: Record "Sales Line";
        SpecCreditLimit: Record "Special Credit Limit";
        SpecCredtLimitLVar: Decimal;
        ItemLedgerLRec: record "Item Ledger Entry";
        SalesRcvble: Record "Sales & Receivables Setup";
        SalesInv: Record "Sales Line";
        CustGMRec: Record customer;
        LocGMrec: Record Location;
        ItemInv: Record "Inventory Posting Setup";
        AvilQty: Decimal;
        Gitem: record item;
        Validation: Codeunit Validations;
        OrderOustAmnt: Decimal;
    BEGIN
        TestField("Location Code");
        TestField("No. Series");
        TestField("Posting No. Series");
        CHECKSalesAppliesDocVal();//PKON22JA6
        //PKONJ17
        IF Rec."Sales Type" <> Rec."Sales Type"::Complimentary THEN BEGIN
            SalesLinRec.Reset();
            SalesLinRec.SetRange("Document Type", "Document Type");
            SalesLinRec.SetRange("Document No.", "No.");
            SalesLinRec.SetRange("Gift Item", false);
            SalesLinRec.SetRange("Unit Price", 0);
            IF SalesLinRec.FindFirst() then
                Error('Unit price should not be blank for line no. %1', SalesLinRec."Line No.");
        END;
        //PKONJ17
        SalesLinRec.reset;
        SalesLinRec.SetRange("Document No.", "No.");
        SalesLinRec.SetRange(Quantity, 0);
        SalesLinRec.SetRange("Gift Item", false); //PKonAUG21
        IF SalesLinRec.findset then
            repeat
                SalesLinRec.Delete();
            until SalesLinRec.next = 0;
        //b2bpksalecorr9 start
        //PKONAU3>>
        //PKONOC28 >>
        /*if ("Document Type" = "Document Type"::order) AND ("POS Window" = false) then begin
            IF ("Gen. Bus. Posting Group" <> 'TRANSFER') AND ("Customer Disc. Group" IN ['DISTRIBU', 'KEYDISTRIB'])
            AND ("Gen. Bus. Posting Group" <> 'RETAILSHOP') THEN begin
                SalesLinRec.reset;
                SalesLinRec.SetRange("Document No.", "No.");
                //SalesLinRec.SetRange(Quantity, 0);
                SalesLinRec.SetRange("Gift Item", true);
                IF Not SalesLinRec.FindFirst() then
                    CheckPromotionLines(0);
                //IF NOT CONFIRM('Customer Eligible for Promotions, Please calculate Promos', true, false) then Exit;//PKONAU20
            end;
        END;
        //PKONAU3<<
        */ //PKONOC28
        SalesLinRec.reset;
        SalesLinRec.SetRange("Document No.", "No.");
        SalesLinRec.SetRange("Document Type", "Document Type");
        SalesLinRec.SetRange(Type, SalesLinRec.Type::Item);
        IF SalesLinRec.findset then
            repeat
                Gitem.get(SalesLinRec."No.");
                Gitem.TESTFIELD("Inventory Posting Group");
                ItemInv.Get("Location Code", Gitem."Inventory Posting Group");
                ItemInv.TestField("Inventory Account");
                ItemInv.TestField("Inventory Account (Interim)");
            until SalesLinRec.next = 0;
        //b2bpksalecorr9 end;
        SalesRcvble.Get();
        IF SalesRcvble."Preventive Negative Quantity" AND CheckStock THEN BEGIN
            SalesLinRec.reset;
            SalesLinRec.SetRange("Document No.", "No.");
            SalesLinRec.SetRange("Document Type", "Document Type");
            SalesLineRec.SetFilter("Outstanding Qty. (Base)", '<>%1', 0);
            IF SalesLinRec.findset then
                repeat
                    clear(AvilQty);
                    Validation.CheckQtyValidations(SalesLinRec);
                until SalesLinRec.next = 0;
        end;

        SalesLinRec.reset;
        SalesLinRec.SetRange("Document No.", "No.");
        IF SalesLinRec.findset then
            repeat
                SalesLinRec.testfield("Location Code");
                //SalesLinRec.TestField(Quantity);
                SalesLinRec.TestField("Unit of Measure");
            //rebate issue

            /*  SalesLinRec.Validate("Inv. Disc. Amount to Invoice");
             SalesLinRec.modify; */

            until SalesLinRec.next = 0;

        if not CustomerPostingGroup.CHIERP_Complimentary then begin
            CustCheck.GET("Sell-to Customer No.");

            CustCrLimitSchdLRec.RESET;
            CustCrLimitSchdLRec.SETFILTER("Customer No.", "Sell-to Customer No.");
            CustCrLimitSchdLRec.SETRANGE("Responsibility Center", "Responsibility Center");
            IF CustCheck."Customer Credit type" = CustCheck."Customer Credit type"::"Cash n Carry" THEN
                CustCrLimitSchdLRec.SETFILTER("Customer Type", '%1', CustCrLimitSchdLRec."Customer Type"::"Cash Customer")
            else
                IF (CustCheck."Customer Credit type" = CustCheck."Customer Credit type"::General) OR (CustCheck."Customer Credit type" = CustCheck."Customer Credit type"::Central) THEN bEGIN
                    CustCrLimitSchdLRec.SETFILTER("Customer Type", '%1', CustCrLimitSchdLRec."Customer Type"::"Credit Customer");
                    CustCrLimitSchdLRec.SETFILTER("Schedule Limit Expiry Date", '>=%1', TODAY);
                end;
            CustCrLimitSchdLRec.SETRANGE(Status, CustCrLimitSchdLRec.Status::Released);
            IF NOT CustCrLimitSchdLRec.FindFirst() then
                Error('Cash\Credit limit setup is not available for this customer.');

            CustCrLimitSchdLRec.RESET;
            CustCrLimitSchdLRec.SETRANGE("Customer No.", "Sell-to Customer No.");
            CustCrLimitSchdLRec.SETRANGE("Responsibility Center", "Responsibility Center");
            CustCrLimitSchdLRec.SETFILTER("Customer Type", '%1', CustCrLimitSchdLRec."Customer Type"::"Credit Customer");
            CustCrLimitSchdLRec.SETFILTER("Schedule Limit Expiry Date", '>=%1', TODAY);
            CustCrLimitSchdLRec.SETRANGE(Status, CustCrLimitSchdLRec.Status::Released);
            IF NOT CustCrLimitSchdLRec.FINDLAST THEN BEGIN
                //PKON22J13-CR220076>>
                IF "Document Type" = "Document Type"::Quote THEN begin
                    SalesLineAmountTotal := 0;
                    SalesLineRec.Reset();//CR230001 B2B19Jan2022
                    SalesLineRec.SETCURRENTKEY("Document Type", "Sell-to Customer No.", "Responsibility Center");
                    SalesLineRec.SETRANGE("Document Type", SalesLineRec."Document Type"::Order);
                    SalesLineRec.SETRANGE("Sell-to Customer No.", "Sell-to Customer No.");
                    SalesLineRec.SETRANGE("Responsibility Center", "Responsibility Center");
                    SalesLineRec.SETRANGE("Order Status", SalesLineRec."Order Status"::" ");
                    IF SalesLineRec.FINDSET THEN
                        REPEAT
                            SalesLineAmountTotal += (SalesLineRec."Outstanding Amount" + SalesLineRec."Shipped Not Invoiced");
                        UNTIL SalesLineRec.NEXT = 0;

                    Clear(OutSalesLineAmountTotal);
                    CustLed2.RESET;
                    CustLed2.SETFILTER("Customer No.", "Sell-to Customer No.");
                    CustLed2.SetRange(Open, true);
                    CustLed2.SetRange("Responsibility Center", "Responsibility Center");
                    IF CustLed2.FINDSET THEN
                        repeat
                            CustLed2.CalcFields("Remaining Amount");
                            OutSalesLineAmountTotal += CustLed2."Remaining Amount";
                        until CustLed2.Next = 0;
                    CalcFields("Amount Including VAT");
                    IF (SalesLineAmountTotal + "Amount Including VAT") > abs(OutSalesLineAmountTotal) THEN
                        Error('Customer Outstanding balance %1 is less than the sales order Outstanding %2 and current Quote value %3 .', ABS(OutSalesLineAmountTotal), SalesLineAmountTotal, "Amount Including VAT");
                end;
                //PKON22J13-CR220076<<
                IF ("Document Type" <> "Document Type"::"Credit Memo") AND ("Document Type" <> "Document Type"::"Return Order")
                    AND ("Document Type" <> "Document Type"::Quote)
                    THEN BEGIN
                    CustCrLimitSchdLRec.RESET;
                    CustCrLimitSchdLRec.SETFILTER("Customer No.", "Sell-to Customer No.");
                    CustCrLimitSchdLRec.SETRANGE("Responsibility Center", "Responsibility Center");
                    CustCrLimitSchdLRec.SETFILTER("Customer Type", '%1', CustCrLimitSchdLRec."Customer Type"::"Cash Customer");
                    CustCrLimitSchdLRec.SETRANGE(Status, CustCrLimitSchdLRec.Status::Released);
                    IF CustCrLimitSchdLRec.FINDLAST THEN BEGIN
                        IF CustCrLimitSchdLRec."Customer Type" = CustCrLimitSchdLRec."Customer Type"::"Cash Customer" THEN BEGIN
                            IF SalRecSetup.GET THEN;
                            IF SalRecSetup."Validate Cust. Credit/Cash" THEN BEGIN
                                IF ("Applies-to Doc. No." = '') AND ("Applies-to ID" = '') then
                                    ERRor('Applies to doc no. or Applies to ID must have a value.');///Pk on 01.15.2021
                                IF "Document Type" = "Document Type"::Order THEN BEGIN
                                    SalesLineAmountTotal := 0;
                                    SalesLineRec.Reset();//CR230001 B2B19Jan2022
                                    SalesLineRec.SETCURRENTKEY("Document Type", "Sell-to Customer No.", "Responsibility Center");
                                    SalesLineRec.SETRANGE("Document Type", SalesLineRec."Document Type"::Order);
                                    SalesLineRec.SETRANGE("Sell-to Customer No.", "Sell-to Customer No.");
                                    SalesLineRec.SETRANGE("Responsibility Center", "Responsibility Center");
                                    SalesLineRec.SETRANGE("Order Status", SalesLineRec."Order Status"::" ");//PKONAU4.2
                                    IF SalesLineRec.FINDSET THEN
                                        REPEAT
                                            //Message('%1...%2...%3..%4', SalesLineRec."Document No.", SalesLineRec."Outstanding Amount", SalesLineRec."Shipped Not Invoiced", SalesLineRec."Unit Price");
                                            SalesLineAmountTotal += SalesLineRec."Outstanding Amount" + SalesLineRec."Shipped Not Invoiced";
                                            ; //Pk on 02.01.2021-Included shiiped and not invoiced value also
                                              //SalesLineAmountTotal += SalesLineRec."Outstanding Amount" + (SalesLineRec."Shipped Not Invoiced" * SalesLineRec."Unit Price");
                                        UNTIL SalesLineRec.NEXT = 0;
                                END;
                                IF (("Applies-to Doc. No." <> '') and ("Applies-to ID" <> '')) THEN BEGIN
                                    CustLed.RESET;
                                    CustLed.SETFILTER("Customer No.", "Sell-to Customer No.");
                                    CustLed.SETFILTER("Applies-to ID", "Applies-to ID");
                                    CustLed.SetRange("Responsibility Center", "Responsibility Center");//PK on 02.05.2021
                                    CustLed.SetRange(Open, true);//PKONOC29
                                    IF CustLed.FINDSET THEN begin
                                        CustLed.CalcSums("Amount to Apply");
                                        CalcFields("Amount Including VAT");
                                        //PK On 12.05.2021
                                        Clear(OutSalesLineAmountTotal);
                                        CustLed2.RESET;
                                        CustLed2.SETFILTER("Customer No.", "Sell-to Customer No.");
                                        CustLed2.SetRange(Open, true);//PKONOC29
                                        CustLed2.SetRange("Responsibility Center", "Responsibility Center");//PK on 02.05.2021
                                        IF CustLed2.FINDSET THEN
                                            repeat
                                                CustLed2.CalcFields("Remaining Amount");
                                                OutSalesLineAmountTotal += CustLed2."Remaining Amount";
                                            until CustLed2.Next = 0;

                                        //PKON22M14>>
                                        IF NOT CheckStock THEN BEGIN
                                            clear(InvoicAmnt);
                                            SalesLinRec.reset;
                                            SalesLinRec.SetRange("Document No.", "No.");
                                            SalesLinRec.SetRange("Document Type", "Document Type");
                                            SalesLinRec.SetFilter("Qty. to Invoice", '<>%1', 0);
                                            IF SalesLinRec.findset then
                                                repeat
                                                    InvoicAmnt += (SalesLinRec."Amount Including VAT" / SalesLinRec.Quantity) * SalesLinRec."Qty. to Invoice";
                                                until SalesLinRec.next = 0;
                                            IF "Applies-to Doc. No." = '' THEN //PKON22AP28.2
                                                IF ABS(CustLed."Amount to Apply") < InvoicAmnt then
                                                    Error('Applied Amount %1 is Less than Invoice Amount %2', ABS(CustLed."Amount to Apply"), InvoicAmnt);
                                        End Else BEGIN
                                            //PKON22M14<<
                                            //PK On 12.05.2021
                                            IF "Applies-to Doc. No." = '' THEN //PKON22AP28.2
                                                IF ABS(CustLed."Amount to Apply") < SalesLineAmountTotal then
                                                    Error('Applied Amount %1 is Less than Sales Order Amount %2', ABS(CustLed."Amount to Apply"), SalesLineAmountTotal);
                                            //PK On 12.05.2021  //PKONOC5
                                            if OutSalesLineAmountTotal >= 0 THEN
                                                Error('Insuffient balance to place order', OutSalesLineAmountTotal, SalesLineAmountTotal)
                                            ELSE
                                                IF ((SalesLineAmountTotal + OutSalesLineAmountTotal) > 0) then
                                                    Error('Insuffient balance to place order. %1..%2', OutSalesLineAmountTotal, SalesLineAmountTotal);
                                            //PK On 12.05.2021
                                        end;
                                    END ELSE
                                        ERROR(Text0002);
                                END ELSE
                                    IF (("Applies-to Doc. No." <> '') and ("Applies-to ID" = '')) THEN BEGIN
                                        CustLed.RESET;
                                        CustLed.SETFILTER("Customer No.", "Sell-to Customer No.");
                                        //CustLed.SETRANGE("Document Type", "Applies-to Doc. Type");
                                        CustLed.SETFILTER("Document No.", "Applies-to Doc. No.");
                                        CustLed.SetRange("Responsibility Center", "Responsibility Center");//PK on 02.05.2021
                                        CustLed.SetRange(open, true);//PKONOC27
                                        IF CustLed.FindSET() then BEGIN
                                            //PK On 12.05.2021
                                            Clear(OutSalesLineAmountTotal);
                                            CustLed2.RESET;
                                            CustLed2.SETFILTER("Customer No.", "Sell-to Customer No.");
                                            CustLed2.SetRange(Open, true);//PKONOC29
                                            CustLed2.SetRange("Responsibility Center", "Responsibility Center");//PK on 02.05.2021
                                            IF CustLed2.FINDSET THEN
                                                repeat
                                                    CustLed2.CalcFields("Remaining Amount");
                                                    OutSalesLineAmountTotal += CustLed2."Remaining Amount";
                                                until CustLed2.Next = 0;
                                            //PK On 12.05.2021
                                            CustLed.CalcSums("Amount to Apply");
                                            CalcFields("Amount Including VAT");
                                            //PKON22AP28.2 >>
                                            //IF ABS(CustLed."Amount to Apply") < "Amount Including VAT" then
                                            CustLed.CalcFields("Remaining Amount");
                                            //PKON22M13>>
                                            clear(OrderOustAmnt);
                                            SalesLinRec.reset;
                                            SalesLinRec.SetRange("Document No.", "No.");
                                            SalesLinRec.SetRange("Document Type", "Document Type");
                                            IF SalesLinRec.findset then
                                                repeat
                                                    OrderOustAmnt += (SalesLinRec."Outstanding Amount" + SalesLinRec."Shipped Not Invoiced");
                                                until SalesLinRec.next = 0;
                                            //IF ABS(CustLed."Remaining Amount") < "Amount Including VAT" then
                                            IF ABS(CustLed."Remaining Amount") < OrderOustAmnt then
                                                Error('Avaliable Amount in selected Doc No. %1 is Lessthan Sales Order Amount %2', ABS(CustLed."Remaining Amount"), "Amount Including VAT");
                                            //PKON22M13<<
                                            //PKON22AP28.2 <<
                                            //IF checkstock THEN BEGIN
                                            if OutSalesLineAmountTotal >= 0 THEN
                                                Error('Insuffient balance to place order', OutSalesLineAmountTotal, SalesLineAmountTotal)
                                            ELSE
                                                IF ((SalesLineAmountTotal + OutSalesLineAmountTotal) > 0) then
                                                    Error('Insuffient balance to place order. %1..%2', OutSalesLineAmountTotal, SalesLineAmountTotal);
                                            //END;
                                            //PK On 12.05.2021
                                        END else
                                            error('Application Entries is not belong to this responsibility centre.')
                                    END ELSE
                                        IF (("Applies-to Doc. No." = '') and ("Applies-to ID" <> '')) THEN BEGIN
                                            CustLed.RESET;
                                            CustLed.SETFILTER("Customer No.", "Sell-to Customer No.");
                                            //CustLed.SETRANGE("Document Type", "Applies-to Doc. Type");
                                            CustLed.SETFILTER("Applies-to ID", "Applies-to ID");
                                            CustLed.SetRange("Responsibility Center", "Responsibility Center");//PK on 02.05.2021
                                            CustLed.SetRange(open, true); //PKONOC27
                                            IF CustLed.FindSET() then BEGIN
                                                //PK On 12.05.2021
                                                Clear(OutSalesLineAmountTotal);
                                                CustLed2.RESET;
                                                CustLed2.SetRange(Open, true);//PKONOC29
                                                CustLed2.SETFILTER("Customer No.", "Sell-to Customer No.");
                                                CustLed2.SetRange("Responsibility Center", "Responsibility Center");//PK on 02.05.2021
                                                IF CustLed2.FINDSET THEN
                                                    repeat
                                                        CustLed2.CalcFields("Remaining Amount");
                                                        OutSalesLineAmountTotal += CustLed2."Remaining Amount";
                                                    until CustLed2.Next = 0;
                                                //PK On 12.05.2021
                                                CalcFields("Amount Including VAT");
                                                CustLed.CalcSums("Amount to Apply");
                                                //PKON22M14>>
                                                IF NOT CheckStock THEN BEGIN
                                                    clear(InvoicAmnt);
                                                    SalesLinRec.reset;
                                                    SalesLinRec.SetRange("Document No.", "No.");
                                                    SalesLinRec.SetRange("Document Type", "Document Type");
                                                    SalesLinRec.SetFilter("Qty. to Invoice", '<>%1', 0);
                                                    IF SalesLinRec.findset then
                                                        repeat
                                                            InvoicAmnt += (SalesLinRec."Amount Including VAT" / SalesLinRec.Quantity) * SalesLinRec."Qty. to Invoice";
                                                        until SalesLinRec.next = 0;
                                                    IF "Applies-to Doc. No." = '' THEN //PKON22AP28.2
                                                        IF ABS(CustLed."Amount to Apply") < InvoicAmnt then
                                                            Error('Applied Amount %1 is Less than Invoice Amount %2', ABS(CustLed."Amount to Apply"), InvoicAmnt);
                                                End Else BEGIN
                                                    //PKON22M14<<
                                                    IF ABS(CustLed."Amount to Apply") < "Amount Including VAT" then
                                                        Error('Applied Amount %1 is Less than Sales Order Amount %2', ABS(CustLed."Amount to Apply"), "Amount Including VAT");
                                                    //PK On 12.05.2021 //PKONOC5
                                                    if OutSalesLineAmountTotal >= 0 THEN
                                                        Error('Insuffient balance to place order', OutSalesLineAmountTotal, SalesLineAmountTotal)
                                                    ELSE
                                                        IF ((SalesLineAmountTotal + OutSalesLineAmountTotal) > 0) then
                                                            Error('Insuffient balance to place order. %1..%2', OutSalesLineAmountTotal, SalesLineAmountTotal);
                                                    //PK On 12.05.2021
                                                end;
                                            END else
                                                error('Application Entries does not exists or not applicable for the document responsibility.')
                                        END;
                            END;
                        END;
                    END;
                END;
            END ELSE begin
                //12.05.2021
                clear(Amt);
                CustLdgLRec.RESET;
                CustLdgLRec.SETRANGE("Customer No.", CustCrLimitSchdLRec."Customer No.");
                CustLdgLRec.SETRANGE("Responsibility Center", CustCrLimitSchdLRec."Responsibility Center");
                CustLdgLRec.SETFILTER(Open, '%1', TRUE);
                IF CustLdgLRec.FINDSET THEN
                    REPEAT
                        CustLdgLRec.CALCFIELDS("Remaining Amt. (LCY)");
                        Amt := Amt + CustLdgLRec."Remaining Amt. (LCY)";
                    UNTIL CustLdgLRec.NEXT = 0;//END //12.05.2021

                AddCuCrLimitGRec.RESET;
                AddCuCrLimitGRec.SETRANGE("Customer No.", CustCrLimitSchdLRec."Customer No.");
                AddCuCrLimitGRec.SETRANGE("Responsibility Center", CustCrLimitSchdLRec."Responsibility Center");
                AddCuCrLimitGRec.SETRANGE("Shortcut Dimension 1 Code", CustCrLimitSchdLRec."Shortcut Dimension 1 Code");
                AddCuCrLimitGRec.SETRANGE("Line No.", CustCrLimitSchdLRec."Line No.");
                AddCuCrLimitGRec.SETFILTER("Add. Cr. End Date", '>=%1', TODAY);
                AddCuCrLimitGRec.SETFILTER("Add. Cr. Start Date", '<=%1', TODAY);
                AddCuCrLimitGRec.SETRANGE(Status, CustCrLimitSchdLRec.Status::Released);
                IF AddCuCrLimitGRec.FINDLAST THEN BEGIN
                    /*clear(Amt); //12.05.2021
                    CustLdgLRec.RESET;
                    CustLdgLRec.SETRANGE("Customer No.", CustCrLimitSchdLRec."Customer No.");
                    CustLdgLRec.SETRANGE("Responsibility Center", CustCrLimitSchdLRec."Responsibility Center");
                    CustLdgLRec.SETFILTER(Open, '%1', TRUE);
                    IF CustLdgLRec.FINDSET THEN
                        CustLdgLRec.CALCFIELDS(CustLdgLRec."Remaining Amount");
                    REPEAT
                        Amt := Amt + CustLdgLRec."Remaining Amount";
                    UNTIL CustLdgLRec.NEXT = 0;*/ //12.05.2021
                END;
                Clear(SalesLineAmountTotal);
                SalesLinRec.reset;
                SalesLinRec.SETCURRENTKEY("Document Type", "Sell-to Customer No.", "Responsibility Center");
                SalesLinRec.SETRANGE("Document Type", SalesLinRec."Document Type"::Order);
                SalesLinRec.SETRANGE("Sell-to Customer No.", "Sell-to Customer No.");
                SalesLinRec.SETRANGE("Responsibility Center", "Responsibility Center");
                SalesLinRec.SETRANGE("Order Status", SalesLinRec."Order Status"::" ");//PKONAU4.2
                IF SalesLinRec.FINDSET THEN
                    REPEAT
                        //SalesLineAmountTotal += SalesLinRec."Outstanding Amount";//Pk on 02.01.2021-Included shiiped and not invoiced value also
                        //SalesLineAmountTotal += SalesLinRec."Outstanding Amount" + (SalesLinRec."Qty. Shipped Not Invoiced" * SalesLinRec."Unit Price");
                        SalesLineAmountTotal += (SalesLinRec."Outstanding Amount" + SalesLinRec."Shipped Not Invoiced");
                    UNTIL SalesLinRec.NEXT = 0;
                //FIX 08Sep2021>>
                if "Document Type" = "Document Type"::Quote then begin
                    SalesLinRec.reset;
                    SalesLinRec.SETCURRENTKEY("Document Type", "Sell-to Customer No.", "Responsibility Center");
                    SalesLinRec.SETRANGE("Document Type", "Document Type");
                    SalesLinRec.SETRANGE("Document No.", "No.");
                    SalesLinRec.SETRANGE("Order Status", SalesLinRec."Order Status"::" ");//PKONAU4.2
                    IF SalesLinRec.FINDSET THEN
                        REPEAT
                            //SalesLineAmountTotal += SalesLinRec."Outstanding Amount";//Pk on 02.01.2021-Included shiiped and not invoiced value also
                            SalesLineAmountTotal += SalesLinRec."Outstanding Amount" + (SalesLinRec."Qty. Shipped Not Invoiced" * SalesLinRec."Unit Price");
                        //SalesLineAmountTotal += (SalesLinRec."Outstanding Amount"+ SalesLinRec."Shipped Not Invoiced");
                        UNTIL SalesLinRec.NEXT = 0;
                end;
                //FIX 08Sep2021<<
                clear(SpecCredtLimitLVar);
                SpecCreditLimit.reset;
                SpecCreditLimit.SetFilter("Starting Date", '<=%1', TODAY);
                SpecCreditLimit.SetFilter("Ending date", '>=%1', Today);
                SpecCreditLimit.SetRange("Approval Status", SpecCreditLimit."Approval Status"::Released);
                IF SpecCreditLimit.FindLast() then begin
                    IF SpecCreditLimit."Sales Type" = SpecCreditLimit."Sales Type"::"All Customers" then
                        SpecCredtLimitLVar := SpecCreditLimit."Credit Limit"
                    else
                        IF (SpecCreditLimit."Sales Type" = SpecCreditLimit."Sales Type"::"Customer Price Group") AND ("Customer Price Group" = SpecCreditLimit."Sales Code") then
                            SpecCredtLimitLVar := SpecCreditLimit."Credit Limit"
                        else
                            IF (SpecCreditLimit."Sales Type" = SpecCreditLimit."Sales Type"::Customer) AND ("Sell-to Customer No." = SpecCreditLimit."Sales Code") then
                                SpecCredtLimitLVar := SpecCreditLimit."Credit Limit";
                end;
                IF ((AddCuCrLimitGRec."Credit Limit" + CustCrLimitSchdLRec."Credit Limit" + SpecCredtLimitLVar) < (Amt + SalesLineAmountTotal)) then
                    Error(Text0003, (AddCuCrLimitGRec."Credit Limit" + CustCrLimitSchdLRec."Credit Limit" + SpecCredtLimitLVar), (Amt + SalesLineAmountTotal));
            END;
        end;
        //PKONOC28>>/*
        SalesLinRec.reset;
        //SalesLinRec.SetRange("Document Type", SalesLinRec."Document Type"::Order);
        SalesLinRec.SetRange("Document No.", "No.");
        SalesLinRec.SetFILTER("Quantity Shipped", '<>%1', 0);
        IF Not SalesLinRec.findFIRST then BEGIN
            IF "Document Type" = "Document Type"::Order THEN begin
                IF ("Sales Type" = "Sales Type"::Export) AND ("Customer Disc. Group" IN ['EXPORT']) then
                    UpdatePromotionLines(0);
                IF ("Sales Type" <> "Sales Type"::Export) AND ("Gen. Bus. Posting Group" <> 'TRANSFER') AND ("Customer Disc. Group" IN ['DISTRIBU', 'KEYDISTRIB'])
        AND ("Gen. Bus. Posting Group" <> 'RETAILSHOP') THEN
                    UpdatePromotionLines(0);
            end;
        END;
        //PKONOC28>>*/


        CheckSalesRestriction(Rec);

    END;

    local procedure CheckSalesRestriction(var SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
        SalesLine2: Record "Sales Line";
        SalesLine3: Record "Sales Line";
        SalesItemRestriction: Record CHIERP_SalesItemRestriction;
        SameQtyErr: Label 'Item %1 must have the quantity base as Item %2\\Refer to Item Sales Restriction Setup', Comment = '%1 is Parent Item No. and %2 is Restriction Item No.';
        MinQuatityErr: Label 'Item %1 quantity base must not be less than %2\\Refer to Item Sales Restriction Setup', Comment = '%1 is Parent Item No. and %2 is Restriction Minimum Quantity';
        MissingItemErr: Label 'Item %1 must be included on the Sales Line\\Refer to Item Sales Restriction Setup', Comment = '%1 is Restriction Item No.';
    begin
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange(Type, SalesLine.Type::Item);
        if SalesLine.FindSet() then
            repeat
                SalesItemRestriction.Reset();
                SalesItemRestriction.SetRange(CHIERP_ParentItemNo, SalesLine."No.");
                SalesItemRestriction.SetFilter(CHIERP_StartingDate, '%1..%2', 0D, SalesHeader."Order Date");
                SalesItemRestriction.SetFilter(CHIERP_EndingDate, '%1|>%2', 0D, SalesHeader."Order Date");
                if SalesItemRestriction.FindFirst() then
                    repeat
                        SalesLine2.Reset();
                        SalesLine2.SetRange("Document Type", SalesHeader."Document Type");
                        SalesLine2.SetRange("Document No.", SalesHeader."No.");
                        SalesLine2.SetRange(Type, SalesLine2.Type::Item);
                        SalesLine2.SetRange("No.", SalesItemRestriction.CHIERP_ItemNo);
                        if SalesLine2.findfirst then begin
                            case SalesItemRestriction.CHIERP_QuantityRestriction of
                                SalesItemRestriction.CHIERP_QuantityRestriction::"Minimum Quantity":
                                    begin
                                        SalesLine2.CalcSums("Quantity (Base)");
                                        if SalesLine2."Quantity (Base)" < SalesItemRestriction.CHIERP_MinimumQuantity then
                                            Error(MinQuatityErr, SalesLine."No.", SalesItemRestriction.CHIERP_MinimumQuantity);
                                    end;
                                SalesItemRestriction.CHIERP_QuantityRestriction::"Same Quantity":
                                    begin
                                        SalesLine2.CalcSums("Quantity (Base)");
                                        SalesLine3.Reset();
                                        SalesLine2.SetRange("Document Type", SalesHeader."Document Type");
                                        SalesLine3.SetRange("Document No.", SalesHeader."No.");
                                        SalesLine3.SetRange(Type, SalesLine2.Type::Item);
                                        SalesLine3.SetRange("No.", SalesItemRestriction.CHIERP_ParentItemNo);
                                        SalesLine3.CalcSums("Quantity (Base)");
                                        if SalesLine2."Quantity (Base)" <> SalesLine3."Quantity (Base)" then
                                            Error(SameQtyErr, SalesLine."No.", SalesItemRestriction.CHIERP_ItemNo);
                                    end;
                            end;
                        end else
                            Error(MissingItemErr, SalesItemRestriction.CHIERP_ItemNo);
                    until SalesItemRestriction.Next() = 0;
            until SalesLine.Next() = 0
        else
            exit;
    end;

    procedure InvoiceCheckSalesMandValues()//PKONJU27
    var
        //CustCrLimitSchdLRec: Record "Cust. Cr. Limit Schedule";
        CustCheck: Record Customer;
        CustLed: record "Cust. Ledger Entry";
        CustLed2: record "Cust. Ledger Entry";
        Text0002: Label 'Apply the document in the Apply entries with the specified Applies to ID.';
        Text0001: Label 'The  %1 or %2 Must not be Blank for a cash Customer';
        SalRecSetup: Record "Sales & Receivables Setup";
        AddCuCrLimitGRec: Record "Add. Cust. Cr. Limit Schedule";
        CustLdgLRec: Record "Cust. Ledger Entry";
        Amt: Decimal;
        Text0003: Label 'Credit Limit Exceeds Credit Limit Available %1 and Sales Order Value %2';
        SalesLinRec: Record "Sales Line";
        SalesLineAmountTotal: Decimal;
        OutSalesLineAmountTotal: Decimal;
        SalesLineRec: Record "Sales Line";
        SpecCreditLimit: Record "Special Credit Limit";
        SpecCredtLimitLVar: Decimal;
        ItemLedgerLRec: record "Item Ledger Entry";
        SalesRcvble: Record "Sales & Receivables Setup";
        SalesInv: Record "Sales Line";
        CustGMRec: Record customer;
        LocGMrec: Record Location;
        ItemInv: Record "Inventory Posting Setup";
        AvilQty: Decimal;
        Gitem: record item;
        Validation: Codeunit Validations;
    BEGIN
        /* IF SalRecSetup.GET THEN;
         IF SalRecSetup."Validate Cust. Credit/Cash" THEN BEGIN
             IF (("Applies-to Doc. No." <> '') and ("Applies-to ID" <> '')) THEN BEGIN
                 CustLed.RESET;
                 CustLed.SETFILTER("Customer No.", "Sell-to Customer No.");
                 CustLed.SETFILTER("Applies-to ID", "Applies-to ID");
                 CustLed.SetRange("Responsibility Center", "Responsibility Center");
                 IF NOT CustLed.FINDFIRST THEN
                     ERROR(Text0002);
             END ELSE
                 IF (("Applies-to Doc. No." <> '') and ("Applies-to ID" = '')) THEN BEGIN
                     CustLed.RESET;
                     CustLed.SETFILTER("Customer No.", "Sell-to Customer No.");
                     CustLed.SETFILTER("Document No.", "Applies-to Doc. No.");
                     CustLed.SetRange("Responsibility Center", "Responsibility Center");
                     IF NOT CustLed.FindFIRST() then
                         ERROR(Text0002)
                 END ELSE
                     IF (("Applies-to Doc. No." = '') and ("Applies-to ID" <> '')) THEN BEGIN
                         CustLed.RESET;
                         CustLed.SETFILTER("Customer No.", "Sell-to Customer No.");
                         CustLed.SETFILTER("Applies-to ID", "Applies-to ID");
                         CustLed.SetRange("Responsibility Center", "Responsibility Center");
                         IF NOT CustLed.FindSET() then
                             ERROR(Text0002)
                     END;
         end; */
    end;

    procedure ShortCloseSalesOrder()
    var
        SaleLineLRec: Record "Sales Line";
        ArchiveManagement: Codeunit ArchiveManagement;
        Text50204: Label 'There is nothing to short close.';
        Text50205: Label 'Do you want to short close the Sales Order?';
        Text50206: Label 'There is no sales line with Shipped Quantity, You can only Close the document.';
        WareHouseLineGrec: Record "Warehouse Shipment Line";
        WarehouseNo: Code[20];
        WarehouseGRec: Record "Warehouse Shipment Header";
        SalQty: Decimal;
        SalQtyInvoiced: Decimal;
        SalQtyShipped: Decimal;
        SalesHdr: Record "Sales Header";
    BEGIN
        /*SalesHdr.Reset();
        SalesHdr.SetRange("Document Type", "Document Type");
        SalesHdr.SetRange("No.", "No.");
        if SalesHdr.FindFirst() then begin
            SalesHdr.Status := SalesHdr.Status::Open;
            SalesHdr.Modify()
        end;*/
        IF NOT ("Order Status" = "Order Status"::"Short Closed") THEN BEGIN
            IF NOT SalesLinesExist THEN
                ERROR(Text50204);
            IF ShippedSalesLinesExist THEN BEGIN
                IF NOT CONFIRM(Text50205, FALSE) THEN
                    EXIT;
                SaleLineLRec.RESET;
                SaleLineLRec.SETRANGE("Document Type", "Document Type");
                SaleLineLRec.SETRANGE("Document No.", "No.");
                IF SaleLineLRec.FINDSET THEN
                    REPEAT
                        IF SaleLineLRec."Quantity Shipped" <> SaleLineLRec."Quantity Invoiced" then
                            Error('Quantities are not invoiced in line no. %1 and for item %2', SaleLineLRec."Line No.", SaleLineLRec."No.");
                    UNTIL SaleLineLRec.NEXT = 0;
                WareHouseLineGrec.Reset();
                WareHouseLineGrec.SetRange("Source No.", "No.");
                if WareHouseLineGrec.FindSet then begin
                    WarehouseNo := WareHouseLineGrec."No.";
                    WareHouseLineGrec.DeleteAll();
                    if WarehouseGRec.Get(WarehouseNo) then
                        WarehouseGRec.Delete();
                END;

                ArchiveManagement.ArchSalesDocumentNoConfirm(Rec);

                //check if rebate variable exits and return balance
                if RebateVariableexits() then
                    ReturnRebateBalance();

                SaleLineLRec.RESET;
                SaleLineLRec.SETRANGE("Document Type", "Document Type");
                SaleLineLRec.SETRANGE("Document No.", "No.");
                SaleLineLRec.SETFILTER("Order Status", '=%1', 0);
                IF SaleLineLRec.FINDSET THEN
                    REPEAT
                        SaleLineLRec.VALIDATE("Actual Order Qty.", SaleLineLRec.Quantity);
                        SaleLineLRec.VALIDATE(Quantity, SaleLineLRec."Quantity Shipped");
                        SaleLineLRec."Order Status" := SaleLineLRec."Order Status"::"Short Closed";
                        SaleLineLRec.MODIFY;
                        SalQty += SaleLineLRec.Quantity;
                        SalQtyShipped += SaleLineLRec."Quantity Shipped";
                        SalQtyInvoiced += SaleLineLRec."Quantity Invoiced";
                    UNTIL SaleLineLRec.NEXT = 0;

                IF (SalQtyInvoiced <> 0) THEN begin
                    IF (SalQtyInvoiced = SalQty) THEN
                        "Order Tracking" := "Order Tracking"::"Completely Invoiced"
                    else
                        "Order Tracking" := "Order Tracking"::"Partially Invoiced"

                END ELSE
                    IF (SalQty = SalQtyShipped) THEN
                        "Order Tracking" := "Order Tracking"::"Completely Shipped";

                "Order Status" := "Order Status"::"Short Closed";
                MODIFY;

            END ELSE
                ERROR(Text50206);
        END;
    END;

    procedure ShortCloseCheck()
    begin
        if not (User.GET(UPPERCASE(USERID())) and User."Short Close Checked") then
            ERROR(UPPERCASE(USERID()) + ' Please enable ShortClosed check Mark in User Setup');
        Status := Status::Open;
    end;

    procedure CalPromtionSchemas()
    begin
        /*
        IF ("Gen. Bus. Posting Group" <> 'TRANSFER') AND ("Customer Disc. Group" IN ['DISTRIBU', 'KEYDISTRIB'])
   AND ("Gen. Bus. Posting Group" <> 'RETAILSHOP') THEN
            UpdatePromotionLines(0)
        else
            Message('No Promo Schemas Available for this customer');*/
        IF ("Gen. Bus. Posting Group" <> 'TRANSFER') AND ("Customer Disc. Group" IN ['DISTRIBU', 'KEYDISTRIB'])
                AND ("Gen. Bus. Posting Group" <> 'RETAILSHOP') THEN
            UpdatePromotionLines(0)
        else
            if ("Sales Type" <> "Sales Type"::Export) AND ("Customer Disc. Group" IN ['EXPORT']) then
                UpdatePromotionLines(0);
        //else
        ///Message('No Promo Schemas Available for this customer');
    end;

    procedure UpdatePromotionLines(SalesLineNoPar: Integer)
    var
        ItemLRec: Record Item;
        SalesLineLRec: Record "Sales Line";
        SalLineLRec: record "Sales Line";
        MinQtyLeft: Decimal;
        TempMinQtyLeft: Decimal;
        ItemLLRec: Record Item;
        MultiplePercentage: Decimal;
        GiftQty: Decimal;
        PromoGiftQty: Decimal;
        TotalQtySold: Decimal;
        PromoSchdLRec: Record "Promo Schedule";
        PromoSchdLineLRec: Record "Promo Schedule Line";
        SaleRecSet: Record "Sales & Receivables Setup";
        //AcctLocAllowed: Boolean;
        TotalOrdRemQty: Decimal;
        CustLrec: Record Customer;
        SalesLine: Record "Sales Line";
        Valid: Codeunit Validations;
    begin
        //Balu 05092021>>
        SaleRecSet.Get();
        if not SaleRecSet."Apply Promotions" then
            exit;
        //Balu 05092021<<
        TESTFIELD(Status, Status::Open);
        CustLrec.get("Sell-to Customer No.");
        SalesLineLRec.RESET;
        SalesLineLRec.SETRANGE("Document Type", "Document Type");
        SalesLineLRec.SETRANGE("Document No.", "No.");
        IF SalesLineLRec.FINDSET THEN
            REPEAT
                SalesLineLRec.VALIDATE("Gift Item Quantity", 0);
                SalesLineLRec.VALIDATE("Promo Qty. Util.", 0);
                IF SalesLineLRec."Gift Item" THEN
                    SalesLineLRec.VALIDATE("Unit Price", 0);
                SalesLineLRec.MODIFY(TRUE);
            UNTIL SalesLineLRec.NEXT = 0;
        //Below Page is Promo. Schedule
        PromoSchdLRec.RESET;
        PromoSchdLRec.SETRANGE("Document Type", PromoSchdLRec."Document Type"::Promo);
        if "Sales Type" = "Sales Type"::Export then
            PromoSchdLRec.SetRange("Export Cust Offers", true)
        else
            PromoSchdLRec.SetRange("Export Cust Offers", false);
        PromoSchdLRec.SETFILTER("Start Date", '<=%1', "Order Date");
        PromoSchdLRec.SETFILTER("End Date", '>=%1', "Order Date");
        PromoSchdLRec.SETRANGE(Status, PromoSchdLRec.Status::Released);
        //FIX06Apr2021>>
        /*IF "POS Window" THEN
            PromoSchdLRec.SETRANGE("Retail Promo", TRUE)
        ELSE
            PromoSchdLRec.SETRANGE("Retail Promo", FALSE);*/
        //FIX06Apr2021<<
        IF PromoSchdLRec.FINDSET THEN begin
            REPEAT
                //Added Prasanna
                IF SaleRecSet.get and SaleRecSet."Calculate Promo On Cust. Grp." THEN BEGIN
                    IF PromoSchdLRec."Application Type" = PromoSchdLRec."Application Type"::Customer then
                        PromoSchdLRec.SetRange("Customer Filter", "Sell-to Customer No.") else
                        IF PromoSchdLRec."Application Type" = PromoSchdLRec."Application Type"::"Customer Posting Group" then
                            PromoSchdLRec.SetRange("Customer Filter", CustLrec."Customer Posting Group");
                end;
                //IF PromoSchdLRec.FINDSET THEN begin
                //Added Prasanna
                PromoSchdLineLRec.RESET;
                PromoSchdLineLRec.SETRANGE("Document Type", PromoSchdLRec."Document Type");
                PromoSchdLineLRec.SETRANGE("Document No.", PromoSchdLRec."No.");
                PromoSchdLineLRec.SETRANGE(Active, TRUE);
                IF "POS Window" THEN
                    PromoSchdLineLRec.SETRANGE("Retail Promo", TRUE)
                ELSE
                    PromoSchdLineLRec.SETRANGE("Retail Promo", FALSE);
                IF PromoSchdLineLRec.FINDSET THEN begin
                    REPEAT
                        CheckPromoRestriction(PromoSchdLRec);
                        IF AcctLocAllowed THEN BEGIN
                            IF PromoSchdLineLRec.Type = PromoSchdLineLRec.Type::Item THEN BEGIN
                                CLEAR(TotalOrdRemQty);
                                SalesLineLRec.RESET;
                                SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                SalesLineLRec.SETRANGE("Document No.", "No.");
                                SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                SalesLineLRec.SETRANGE("No.", PromoSchdLineLRec."No.");
                                SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                IF SalesLineLRec.FINDSET THEN BEGIN
                                    REPEAT
                                        SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");
                                        ItemLRec.GET(SalesLineLRec."No.");
                                        TotalOrdRemQty += (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                          GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code")) * SalesQty;
                                        TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                    UNTIL SalesLineLRec.NEXT = 0;
                                    REPEAT
                                        CLEAR(NoOfferLineExists);
                                        PromoOfferLineLGRec.RESET;
                                        PromoOfferLineLGRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                        PromoOfferLineLGRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                        PromoOfferLineLGRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                        PromoOfferLineLGRec.SETRANGE(Active, TRUE);
                                        IF PromoOfferLineLGRec.FINDFIRST THEN
                                            //Below Page For Promo. Schd. Offer Lines
                                            IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::"Fixed Quantity" THEN BEGIN
                                                PromoOfferLineLRec.RESET;
                                                PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                PromoOfferLineLRec.SETFILTER("Promotion Type", '%1', PromoOfferLineLRec."Promotion Type"::"Fixed Quantity");
                                                PromoOfferLineLRec.SETFILTER("Min. Quantity", '<=%1', TotalOrdRemQty);
                                                PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                                PromoOfferLineLRec.ASCENDING(TRUE);
                                                IF PromoOfferLineLRec.FINDLAST THEN BEGIN
                                                    MinQtyLeft := PromoOfferLineLRec."Min. Quantity";
                                                    SalesLineLRec.RESET;
                                                    SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                    SalesLineLRec.SETRANGE("Document No.", "No.");
                                                    SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                    SalesLineLRec.SETRANGE("No.", PromoSchdLineLRec."No.");
                                                    SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                    IF SalesLineLRec.FINDSET THEN
                                                        REPEAT
                                                            SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");
                                                            ItemLRec.GET(SalesLineLRec."No.");
                                                            SalesQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                              GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code"))
                                                                              * SalesQty;
                                                            SalesQty := ROUND(SalesQty, 1.0, '=');
                                                            IF SalesQty > MinQtyLeft THEN
                                                                SalesQty := MinQtyLeft;
                                                            MinQtyLeft -= SalesQty;
                                                            SalesQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code") /
                                                                              GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                              * SalesQty;
                                                            IF SalesQty <> 0 THEN BEGIN
                                                                SalesLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                SalesLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                            END;
                                                            SalesLineLRec."Promo Qty. Util." += SalesQty;
                                                            SalesLineLRec.MODIFY;
                                                        UNTIL SalesLineLRec.NEXT = 0;
                                                    // Updating the Promotion Qty Utilized <<
                                                    SalLineLRec.RESET;
                                                    SalLineLRec.SETRANGE("Document Type", "Document Type");
                                                    SalLineLRec.SETRANGE("Document No.", "No.");
                                                    SalLineLRec.SETRANGE(Type, SalLineLRec.Type::Item);
                                                    SalLineLRec.SETFILTER("No.", PromoOfferLineLRec."Gift Item No.");
                                                    SalLineLRec.SETRANGE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                    SalLineLRec.SETRANGE("Gift Item", TRUE);
                                                    IF SalLineLRec.FINDFIRST THEN BEGIN
                                                        //SalLineLRec."Gift Item Quantity" += PromoOfferLineLRec."Promotion Quantity";
                                                        //SalLineLRec."Gift Item Quantity" := PromoOfferLineLRec."Promotion Quantity";
                                                        //SalLineLRec.VALIDATE("Gift Item Quantity");
                                                        //SalLineLRec.VALIDATE("Unit Price",0);
                                                        //SalLineLRec.MODIFY(TRUE);
                                                    END ELSE BEGIN
                                                        SalLineLRec.INIT;
                                                        SalLineLRec."Document Type" := SalesLineLRec."Document Type";
                                                        SalLineLRec."Document No." := SalesLineLRec."Document No.";
                                                        ReturnSalesLineNo(SalesLineLRec, PromoSchdLineLRec."Document No.", PromoSchdLineLRec."Line No.");
                                                        SalLineLRec."Line No." := (LineNoGVar + 100);
                                                        //SalLineLRec.VALIDATE("Sell-to Customer No.",SalesLineLRec."Sell-to Customer No.");
                                                        IF "POS Window" THEN
                                                            SalLineLRec.Validate("POS Window", true);
                                                        SalLineLRec.VALIDATE("Sell-to Customer No.", SalesLineLRec."Sell-to Customer No.");
                                                        SalLineLRec.Type := SalesLineLRec.Type;
                                                        //SAA3.0 >>
                                                        //SalLineLRec."Shortcut Dimension 1 Code":=SalesLineLRec."Shortcut Dimension 1 Code";
                                                        //SalLineLRec."Shortcut Dimension 2 Code":=SalesLineLRec."Shortcut Dimension 2 Code";
                                                        //SalLineLRec.VALIDATE("Item Category Code",SalesLineLRec."Item Category Code");
                                                        //SalLineLRec.VALIDATE("Product Group Code", SalesLineLRec."Product Group Code");B2B
                                                        SalLineLRec.VALIDATE("VAT Bus. Posting Group", SalesLineLRec."VAT Bus. Posting Group");
                                                        SalLineLRec.INSERT;
                                                        SalLineLRec.VALIDATE("Item Category Code", SalesLineLRec."Item Category Code");
                                                        SalLineLRec.VALIDATE(Type, SalesLineLRec.Type);
                                                        //SalLineLRec.VALIDATE("Shortcut Dimension 1 Code",SalesLineLRec."Shortcut Dimension 1 Code");
                                                        //SalLineLRec.VALIDATE("Shortcut Dimension 2 Code",SalesLineLRec."Shortcut Dimension 2 Code");
                                                        //SAA3.0 >>
                                                        //GJ_CHIPOS_RKD_221013 >>>>

                                                        IF "POS Window" THEN
                                                            SalLineLRec.VALIDATE("POS Window", TRUE);
                                                        //GJ_CHIPOS_RKD_221013 <<<<

                                                        IF ItemLLRec.GET(PromoOfferLineLRec."Gift Item No.") THEN;
                                                        SalLineLRec.VALIDATE("Item Category Code", ItemLLRec."Item Category Code");
                                                        //SalLineLRec.VALIDATE("Product Group Code", ItemLLRec."Product Group Code");//B2B
                                                        SalLineLRec.VALIDATE("Shortcut Dimension 3 Code", ItemLLRec."Shortcut Dimension 3 Code");
                                                        SalLineLRec.VALIDATE("No.", PromoOfferLineLRec."Gift Item No.");
                                                        SalLineLRec.VALIDATE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");

                                                        //RKD >> for Promo Items for Direct Sales order
                                                        //IF SalesHeader2.GET(SalesLineLRec."Document Type",SalesLineLRec."Document No.") THEN
                                                        //IF "Direct Sales Order" THEN B2B
                                                        IF "Sales Type" = "Sales Type"::Direct THEN
                                                            SalLineLRec."Location Code" := SalesLineLRec."Location Code"
                                                        ELSE
                                                            //RKD<<
                                                            SalLineLRec."Location Code" := SalesLineLRec."Location Code"; //Validation removed for Lekki

                                                        SalLineLRec.VALIDATE("Gift Item Quantity", PromoOfferLineLRec."Promotion Quantity");
                                                        SalLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                        SalLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                        SalLineLRec."Promo. Offer Line No." := PromoOfferLineLRec."Line No.";
                                                        SalLineLRec.VALIDATE("Unit Price", 0);
                                                        PromoGrpHeadLRec.SETRANGE("No.", PromoOfferLineLRec."Promo. Schd. Document No.");
                                                        IF PromoGrpHeadLRec.FINDFIRST THEN
                                                            //IF PromoGrpHeadLRec.GET(PromoOfferLineLRec."Promo. Schd. Document No.") THEN
                                                            SalLineLRec.VALIDATE("Gen. Bus. Posting Group", PromoGrpHeadLRec."Gen. Bus. Posting Group");
                                                        SalLineLRec."Gift Item" := TRUE;
                                                        SalLineLRec.MODIFY;
                                                    END;
                                                    TotalOrdRemQty -= PromoOfferLineLRec."Min. Quantity";
                                                END ELSE
                                                    NoOfferLineExists := TRUE;
                                            END;
                                        //for Percentage Offers
                                        IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::Percentage THEN BEGIN
                                            PromoOfferLineLRec.RESET;
                                            //PromoOfferLineLRec.SETCURRENTKEY("Min. Quantity");
                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");

                                            //PromoOfferLineLRec.SETFILTER("Min. Quantity",'<=%1',TotalOrdRemQty);
                                            PromoOfferLineLRec.SETFILTER("Promotion Type", '%1', PromoOfferLineLRec."Promotion Type"::Percentage);
                                            PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                            PromoOfferLineLRec.ASCENDING(TRUE);
                                            IF PromoOfferLineLRec.FINDFIRST THEN BEGIN
                                                // Updating the Promotion Qty Utilized >>
                                                MinQtyLeft := PromoOfferLineLRec."Min. Quantity";
                                                MultiplePercentage := ROUND(((100 * PromoOfferLineLRec."Promotion Quantity")
                                                  / PromoOfferLineLRec."Min. Quantity"), 1);
                                                PromoGiftQty := ROUND(MultiplePercentage * TotalOrdRemQty / 100, 1, '<');

                                                SalesLineLRec.RESET;
                                                SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                SalesLineLRec.SETRANGE("Document No.", "No.");
                                                SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                SalesLineLRec.SETRANGE("No.", PromoSchdLineLRec."No.");
                                                SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                IF SalesLineLRec.FINDSET THEN
                                                    REPEAT

                                                        SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");

                                                        ItemLRec.GET(SalesLineLRec."No.");
                                                        //SalesQty := (UOMMgt.GetQtyPerUnitOfMeasure(ItemLRec,SalesLineLRec."Unit of Measure Code")/
                                                        //                UOMMgt.GetQtyPerUnitOfMeasure(ItemLRec,PromoGrpLineLRec."Unit of Measure Code"))
                                                        //               * SalesQty;  //SAA3.0
                                                        SalesQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                          GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code"))
                                                                          * SalesQty;

                                                        SalesQty := ROUND(SalesQty, 1.0, '=');


                                                        IF SalesQty > MinQtyLeft THEN
                                                            SalesQty := MinQtyLeft;

                                                        MinQtyLeft -= SalesQty;

                                                        //SalesQty := (UOMMgt.GetQtyPerUnitOfMeasure(ItemLRec,PromoGrpLineLRec."Unit of Measure Code") /
                                                        //                 UOMMgt.GetQtyPerUnitOfMeasure(ItemLRec,SalesLineLRec."Unit of Measure Code"))
                                                        //               * SalesQty; //SAA3.0
                                                        SalesQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code") /
                                                                          GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                          * SalesQty;

                                                        IF SalesQty <> 0 THEN BEGIN
                                                            SalesLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                            SalesLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                        END;
                                                        SalesLineLRec."Promo Qty. Util." += SalesQty;
                                                        SalesLineLRec.MODIFY;
                                                    UNTIL SalesLineLRec.NEXT = 0;
                                                // Updating the Promotion Qty Utilized <<

                                                SalLineLRec.RESET;
                                                SalLineLRec.SETRANGE("Document Type", "Document Type");
                                                SalLineLRec.SETRANGE("Document No.", "No.");
                                                SalLineLRec.SETRANGE(Type, SalLineLRec.Type::Item);
                                                SalLineLRec.SETFILTER("No.", PromoOfferLineLRec."Gift Item No.");
                                                SalLineLRec.SETRANGE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                SalLineLRec.SETRANGE("Gift Item", TRUE);
                                                IF SalLineLRec.FINDFIRST THEN BEGIN
                                                    //SalLineLRec."Gift Item Quantity" += PromoOfferLineLRec."Promotion Quantity";
                                                    //SalLineLRec."Gift Item Quantity" := PromoOfferLineLRec."Promotion Quantity";
                                                    //SalLineLRec.VALIDATE("Gift Item Quantity");
                                                    //SalLineLRec.VALIDATE("Unit Price",0);
                                                    //SalLineLRec.MODIFY(TRUE);
                                                END ELSE BEGIN
                                                    SalLineLRec.INIT;
                                                    SalLineLRec."Document Type" := SalesLineLRec."Document Type";
                                                    SalLineLRec."Document No." := SalesLineLRec."Document No.";
                                                    ReturnSalesLineNo(SalesLineLRec, PromoSchdLineLRec."Document No.", PromoSchdLineLRec."Line No.");
                                                    SalLineLRec."Line No." := (LineNoGVar + 100);
                                                    //SalLineLRec.VALIDATE("Sell-to Customer No.",SalesLineLRec."Sell-to Customer No.");
                                                    //GJ_CHIPOS_RKD_221013 >>>>
                                                    IF "POS Window" THEN
                                                        SalLineLRec.VALIDATE("POS Window", TRUE);
                                                    //GJ_CHIPOS_RKD_221013 <<<<

                                                    SalLineLRec.VALIDATE("Sell-to Customer No.", SalesLineLRec."Sell-to Customer No.");
                                                    SalLineLRec.Type := SalesLineLRec.Type;
                                                    //SAA3.0 >>
                                                    SalLineLRec.VALIDATE("Item Category Code", SalesLineLRec."Item Category Code");
                                                    //SalLineLRec.VALIDATE("Product Group Code", SalesLineLRec."Product Group Code");//b2b
                                                    SalLineLRec.VALIDATE("VAT Bus. Posting Group", SalesLineLRec."VAT Bus. Posting Group");
                                                    //SalLineLRec."Shortcut Dimension 1 Code":=SalesLineLRec."Shortcut Dimension 1 Code";
                                                    //SalLineLRec."Shortcut Dimension 2 Code":=SalesLineLRec."Shortcut Dimension 2 Code";

                                                    SalLineLRec.INSERT;

                                                    SalLineLRec.VALIDATE(Type, SalesLineLRec.Type);
                                                    //SalLineLRec.VALIDATE("Shortcut Dimension 1 Code",SalesLineLRec."Shortcut Dimension 1 Code");
                                                    //SalLineLRec.VALIDATE("Shortcut Dimension 2 Code",SalesLineLRec."Shortcut Dimension 2 Code");

                                                    //SAA3.0 >>
                                                    //GJ_CHIPOS_RKD_221013 >>>>
                                                    IF "POS Window" THEN
                                                        SalLineLRec.VALIDATE("POS Window", TRUE);
                                                    //GJ_CHIPOS_RKD_221013 <<<<

                                                    IF ItemLLRec.GET(PromoOfferLineLRec."Gift Item No.") THEN;
                                                    SalLineLRec.VALIDATE("Item Category Code", ItemLLRec."Item Category Code");
                                                    // SalLineLRec.VALIDATE("Product Group Code", ItemLLRec."Product Group Code");//b2b

                                                    SalLineLRec.VALIDATE("Shortcut Dimension 3 Code", ItemLLRec."Shortcut Dimension 3 Code");
                                                    SalLineLRec.VALIDATE("No.", PromoOfferLineLRec."Gift Item No.");
                                                    SalLineLRec.VALIDATE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                    //RKD <<
                                                    //IF SalesHeader2.GET(SalesLineLRec."Document Type",SalesLineLRec."Document No.") THEN
                                                    //IF "Direct Sales Order" THEN @B2B
                                                    IF "Sales Type" = "Sales Type"::Direct then
                                                        SalLineLRec."Location Code" := SalesLineLRec."Location Code"
                                                    ELSE
                                                        //RKD >>
                                                        SalLineLRec."Location Code" := SalesLineLRec."Location Code";  //validation removed for lekki

                                                    //SalLineLRec.VALIDATE("Gift Item Quantity",PromoOfferLineLRec."Promotion Quantity");
                                                    SalLineLRec.VALIDATE("Gift Item Quantity", PromoGiftQty);
                                                    SalLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                    SalLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                    SalLineLRec."Promo. Offer Line No." := PromoOfferLineLRec."Line No.";
                                                    SalLineLRec.VALIDATE("Unit Price", 0);
                                                    PromoGrpHeadLRec.SETRANGE("No.", PromoOfferLineLRec."Promo. Schd. Document No.");
                                                    IF PromoGrpHeadLRec.FINDFIRST THEN
                                                        //IF PromoGrpHeadLRec.GET(PromoOfferLineLRec."Promo. Schd. Document No.") THEN
                                                        SalLineLRec.VALIDATE("Gen. Bus. Posting Group", PromoGrpHeadLRec."Gen. Bus. Posting Group");
                                                    SalLineLRec."Gift Item" := TRUE;
                                                    SalLineLRec.MODIFY;
                                                END;
                                                TotalOrdRemQty -= PromoOfferLineLRec."Min. Quantity";
                                            END ELSE
                                                NoOfferLineExists := TRUE;
                                        END;

                                    UNTIL (TotalOrdRemQty <= 0) OR NoOfferLineExists;
                                    // Checking for Offer Lines with respective offer quantity <<
                                END;
                            END ELSE
                                IF PromoSchdLineLRec.Type = PromoSchdLineLRec.Type::"Promo Group" THEN BEGIN
                                    PromoGrpHeadLRec.RESET;
                                    PromoGrpHeadLRec.SETRANGE("Document Type", PromoGrpHeadLRec."Document Type"::"Promo Group");
                                    PromoGrpHeadLRec.SETRANGE("No.", PromoSchdLineLRec."No.");
                                    //GJ_CHIPOS_RKD_191013 >>>>
                                    IF "POS Window" THEN
                                        PromoGrpHeadLRec.SETRANGE("Retail Promo", TRUE)
                                    ELSE
                                        PromoGrpHeadLRec.SETRANGE("Retail Promo", FALSE);
                                    //GJ_CHIPOS_RKD_191013 <<<<
                                    IF PromoGrpHeadLRec.FINDFIRST THEN BEGIN
                                        IF PromoGrpHeadLRec."Promo Type" = PromoGrpHeadLRec."Promo Type"::" " THEN BEGIN
                                            REPEAT
                                                CLEAR(PromoExists);
                                                OfferValid := TRUE;
                                                // Checking for Promotion Group is valid for Sales Order Qty. to update free gift items >>
                                                PromoGrpLineLRec.RESET;
                                                PromoGrpLineLRec.SETRANGE("Document Type", PromoGrpLineLRec."Document Type"::"Promo Group");
                                                PromoGrpLineLRec.SETRANGE("Document No.", PromoSchdLineLRec."No.");
                                                //GJ_CHIPOS_RKD_191013 >>>>
                                                IF "POS Window" THEN
                                                    PromoGrpLineLRec.SETRANGE("Retail Promo", TRUE)
                                                ELSE
                                                    PromoGrpLineLRec.SETRANGE("Retail Promo", FALSE);
                                                //GJ_CHIPOS_RKD_191013 <<<<
                                                IF PromoGrpLineLRec.FINDSET THEN BEGIN
                                                    REPEAT
                                                        SalesLineLRec.RESET;
                                                        SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                        SalesLineLRec.SETRANGE("Document No.", "No.");
                                                        SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                        SalesLineLRec.SETRANGE("No.", PromoGrpLineLRec."No.");
                                                        SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                        IF SalesLineLRec.FINDSET THEN BEGIN
                                                            CLEAR(TotalOrdRemQty);
                                                            CheckPromoRestriction(PromoSchdLRec);//SAA3.0
                                                            REPEAT
                                                                // Checking Sales Remaining Qty & Changing into Promogroup Item UOM >>
                                                                SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");

                                                                ItemLRec.GET(SalesLineLRec."No.");
                                                                TotalOrdRemQty += (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpLineLRec."Unit of Measure Code")) * SalesQty;
                                                            // Checking Sales Remaining Qty & Changing into Promogroup Item UOM <<
                                                            UNTIL SalesLineLRec.NEXT = 0;
                                                            TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');

                                                            IF TotalOrdRemQty <> 0 THEN BEGIN
                                                                IF (PromoGrpLineLRec."Minimum Qty." = 0) THEN
                                                                    OfferValid := FALSE;
                                                            END;

                                                            IF TotalOrdRemQty < PromoGrpLineLRec."Minimum Qty." THEN
                                                                OfferValid := FALSE;

                                                            PromoExists := TRUE;
                                                        END ELSE
                                                            OfferValid := FALSE;

                                                    UNTIL (PromoGrpLineLRec.NEXT = 0) OR NOT OfferValid;

                                                    // To skip loop if no promotion lines exists on sales order >>
                                                    IF NOT PromoExists THEN
                                                        OfferValid := FALSE;
                                                    // To skip loop if no promotion lines exists on sales order <<

                                                    //  update gift items when the promo group is valid >>

                                                    IF OfferValid THEN BEGIN
                                                        PromoGrpLineLRec.RESET;
                                                        PromoGrpLineLRec.SETRANGE("Document Type", PromoGrpLineLRec."Document Type"::"Promo Group");
                                                        PromoGrpLineLRec.SETRANGE("Document No.", PromoSchdLineLRec."No.");
                                                        //GJ_CHIPOS_RKD_191013 >>>>
                                                        IF "POS Window" THEN
                                                            PromoGrpLineLRec.SETRANGE("Retail Promo", TRUE)
                                                        ELSE
                                                            PromoGrpLineLRec.SETRANGE("Retail Promo", FALSE);
                                                        //GJ_CHIPOS_RKD_191013 <<<<
                                                        IF PromoGrpLineLRec.FINDSET THEN
                                                            REPEAT
                                                                MinQtyLeft := PromoGrpLineLRec."Minimum Qty.";
                                                                SalesLineLRec.RESET;
                                                                SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                                SalesLineLRec.SETRANGE("Document No.", "No.");
                                                                SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                                SalesLineLRec.SETRANGE("No.", PromoGrpLineLRec."No.");
                                                                SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                                IF SalesLineLRec.FINDSET THEN
                                                                    REPEAT
                                                                        SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");

                                                                        ItemLRec.GET(SalesLineLRec."No.");
                                                                        TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                                          GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpLineLRec."Unit of Measure Code"))
                                                                                          * SalesQty;
                                                                        TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');


                                                                        IF TotalOrdRemQty <= MinQtyLeft THEN BEGIN
                                                                            TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpLineLRec."Unit of Measure Code") /
                                                                                               GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                                               * TotalOrdRemQty;
                                                                            TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                                                        END ELSE BEGIN
                                                                            TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpLineLRec."Unit of Measure Code") /
                                                                                               GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                                               * MinQtyLeft;
                                                                            TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                                                        END;
                                                                        IF TotalOrdRemQty <> 0 THEN BEGIN
                                                                            SalesLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                            SalesLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                                        END;
                                                                        SalesLineLRec."Promo Qty. Util." += TotalOrdRemQty;
                                                                        SalesLineLRec.MODIFY;

                                                                        TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                                          GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpLineLRec."Unit of Measure Code"))
                                                                                          * TotalOrdRemQty;
                                                                        TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');

                                                                        MinQtyLeft -= TotalOrdRemQty;
                                                                    UNTIL SalesLineLRec.NEXT = 0;
                                                            UNTIL PromoGrpLineLRec.NEXT = 0;
                                                        //To filter Promotion Type
                                                        PromoOfferLineLGRec.RESET;
                                                        PromoOfferLineLGRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                        PromoOfferLineLGRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                        PromoOfferLineLGRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                        //PromoOfferLineLGRec.SETFILTER("Min. Quantity",'<=%1',TotalOrdRemQty);
                                                        PromoOfferLineLGRec.SETRANGE(Active, TRUE);
                                                        IF PromoOfferLineLGRec.FINDFIRST THEN
                                                            IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::"Fixed Quantity" THEN BEGIN
                                                                PromoOfferLineLRec.RESET;
                                                                PromoOfferLineLRec.SETCURRENTKEY("Min. Quantity");
                                                                PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                                PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                                PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                                PromoOfferLineLRec.SETFILTER("Min. Quantity", '<=%1', TotalOrdRemQty);
                                                                PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                                                PromoOfferLineLRec.ASCENDING(TRUE);
                                                                IF PromoOfferLineLRec.FINDLAST THEN
                                                                    SalLineLRec.RESET;
                                                                SalLineLRec.SETRANGE("Document Type", "Document Type");
                                                                SalLineLRec.SETRANGE("Document No.", "No.");
                                                                SalLineLRec.SETRANGE(Type, SalLineLRec.Type::Item);
                                                                SalLineLRec.SETFILTER("No.", PromoOfferLineLRec."Gift Item No.");
                                                                SalLineLRec.SETRANGE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                SalLineLRec.SETRANGE("Gift Item", TRUE);
                                                                IF SalLineLRec.FINDFIRST THEN BEGIN
                                                                    //SalLineLRec."Gift Item Quantity" += PromoOfferLineLRec."Promotion Quantity";
                                                                    //SalLineLRec.VALIDATE("Gift Item Quantity");
                                                                    //SalLineLRec.VALIDATE("Unit Price",0);
                                                                    //SalLineLRec.MODIFY(TRUE);
                                                                END ELSE BEGIN
                                                                    SalLineLRec.INIT;
                                                                    SalLineLRec."Document Type" := SalesLineLRec."Document Type";
                                                                    SalLineLRec."Document No." := SalesLineLRec."Document No.";
                                                                    ReturnSalesLineNo(SalesLineLRec, PromoSchdLineLRec."Document No.", PromoSchdLineLRec."Line No.");
                                                                    SalLineLRec."Line No." := (LineNoGVar + 100);
                                                                    //SalLineLRec.VALIDATE("Sell-to Customer No.",SalesLineLRec."Sell-to Customer No.");
                                                                    //SalLineLRec."Shortcut Dimension 1 Code":=SalesLineLRec."Shortcut Dimension 1 Code";
                                                                    //SalLineLRec."Shortcut Dimension 2 Code":=SalesLineLRec."Shortcut Dimension 2 Code";
                                                                    //GJ_CHIPOS_RKD_221013 >>>>
                                                                    IF "POS Window" THEN
                                                                        SalLineLRec.VALIDATE("POS Window", TRUE);
                                                                    //GJ_CHIPOS_RKD_221013 <<<<

                                                                    SalLineLRec.INSERT;
                                                                    SalLineLRec.VALIDATE("Sell-to Customer No.", SalesLineLRec."Sell-to Customer No.");
                                                                    SalLineLRec.VALIDATE(Type, SalesLineLRec.Type);
                                                                    //GJ_CHIPOS_RKD_221013 >>>>
                                                                    IF "POS Window" THEN
                                                                        SalLineLRec.VALIDATE("POS Window", TRUE);
                                                                    //GJ_CHIPOS_RKD_221013 <<<<

                                                                    //SalLineLRec.VALIDATE("Shortcut Dimension 1 Code",SalesLineLRec."Shortcut Dimension 1 Code");
                                                                    //SalLineLRec.VALIDATE("Shortcut Dimension 2 Code",SalesLineLRec."Shortcut Dimension 2 Code");
                                                                    //SAA3.0 >>
                                                                    SalLineLRec.VALIDATE("Item Category Code", SalesLineLRec."Item Category Code");
                                                                    //SalLineLRec.VALIDATE("Product Group Code", SalesLineLRec."Product Group Code");//B2B
                                                                    SalLineLRec.VALIDATE("VAT Bus. Posting Group", SalesLineLRec."VAT Bus. Posting Group");
                                                                    //SAA3.0 >>
                                                                    IF ItemLLRec.GET(PromoOfferLineLRec."Gift Item No.") THEN;
                                                                    SalLineLRec.VALIDATE("Shortcut Dimension 3 Code", ItemLLRec."Shortcut Dimension 3 Code");
                                                                    SalLineLRec.VALIDATE("No.", PromoOfferLineLRec."Gift Item No.");
                                                                    SalLineLRec.VALIDATE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                    //RKD<<
                                                                    //IF SalesHeader2.GET(SalesLineLRec."Document Type",SalesLineLRec."Document No.") THEN
                                                                    //IF "Direct Sales Order" THEN B2B
                                                                    IF "Sales Type" = "Sales Type"::Direct then
                                                                        SalLineLRec."Location Code" := SalesLineLRec."Location Code"
                                                                    ELSE
                                                                        //RKD >>
                                                                        SalLineLRec."Location Code" := SalesLineLRec."Location Code"; //validation removed for lekki

                                                                    SalLineLRec.VALIDATE("Gift Item Quantity", PromoOfferLineLRec."Promotion Quantity");
                                                                    SalLineLRec.VALIDATE("Unit Price", 0);
                                                                    SalLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                    SalLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                                    SalLineLRec."Promo. Offer Line No." := PromoOfferLineLRec."Line No.";
                                                                    SalLineLRec."Gift Item" := TRUE;
                                                                    PromoGrpHeadLRec.SETRANGE("No.", PromoOfferLineLRec."Promo. Schd. Document No.");
                                                                    IF PromoGrpHeadLRec.FINDFIRST THEN
                                                                        //IF PromoGrpHeadLRec.GET(PromoOfferLineLRec."Promo. Schd. Document No.") THEN
                                                                        SalLineLRec.VALIDATE("Gen. Bus. Posting Group", PromoGrpHeadLRec."Gen. Bus. Posting Group");
                                                                    SalLineLRec.MODIFY;
                                                                END;
                                                                //UNTIL PromoOfferLineLRec.NEXT = 0;
                                                            END;
                                                        IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::Percentage THEN BEGIN
                                                            PromoOfferLineLRec.RESET;
                                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                            PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                                            IF PromoOfferLineLRec.FINDSET THEN
                                                                REPEAT
                                                                    //Find Percentage and promo gift quantity
                                                                    //MultiplePercentage:= ROUND(((100 * PromoOfferLineLRec."Promotion Quantity")
                                                                    /// PromoOfferLineLRec."Min. Quantity"),1);
                                                                    //PromoGiftQty := ROUND(MultiplePercentage * TotalOrdRemQty / 100,1,'<');
                                                                    PromoGiftQty := ROUND(((10 * PromoOfferLineLRec."Promotion Quantity") / 100)
                                                                          * TotalQtySold, 1);


                                                                    SalLineLRec.RESET;
                                                                    SalLineLRec.SETRANGE("Document Type", "Document Type");
                                                                    SalLineLRec.SETRANGE("Document No.", "No.");
                                                                    SalLineLRec.SETRANGE(Type, SalLineLRec.Type::Item);
                                                                    SalLineLRec.SETFILTER("No.", PromoOfferLineLRec."Gift Item No.");
                                                                    SalLineLRec.SETRANGE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                    SalLineLRec.SETRANGE("Gift Item", TRUE);
                                                                    IF SalLineLRec.FINDFIRST THEN BEGIN
                                                                        //SalLineLRec."Gift Item Quantity" += PromoOfferLineLRec."Promotion Quantity";
                                                                        //SalLineLRec.VALIDATE("Gift Item Quantity");
                                                                        //SalLineLRec.VALIDATE("Unit Price",0);
                                                                        //SalLineLRec.MODIFY(TRUE);
                                                                    END ELSE BEGIN
                                                                        SalLineLRec.INIT;
                                                                        SalLineLRec."Document Type" := SalesLineLRec."Document Type";
                                                                        SalLineLRec."Document No." := SalesLineLRec."Document No.";
                                                                        ReturnSalesLineNo(SalesLineLRec, PromoSchdLineLRec."Document No.", PromoSchdLineLRec."Line No.");
                                                                        SalLineLRec."Line No." := (LineNoGVar + 100);
                                                                        //SalLineLRec.VALIDATE("Sell-to Customer No.",SalesLineLRec."Sell-to Customer No.");
                                                                        //SalLineLRec."Shortcut Dimension 1 Code":=SalesLineLRec."Shortcut Dimension 1 Code";
                                                                        //SalLineLRec."Shortcut Dimension 2 Code":=SalesLineLRec."Shortcut Dimension 2 Code";
                                                                        //GJ_CHIPOS_RKD_221013 >>>>
                                                                        IF "POS Window" THEN
                                                                            SalLineLRec.VALIDATE("POS Window", TRUE);
                                                                        //GJ_CHIPOS_RKD_221013 <<<<

                                                                        SalLineLRec.INSERT;
                                                                        SalLineLRec.VALIDATE("Sell-to Customer No.", SalesLineLRec."Sell-to Customer No.");
                                                                        SalLineLRec.VALIDATE(Type, SalesLineLRec.Type);
                                                                        //SalLineLRec.VALIDATE("Shortcut Dimension 1 Code",SalesLineLRec."Shortcut Dimension 1 Code");
                                                                        //SalLineLRec.VALIDATE("Shortcut Dimension 2 Code",SalesLineLRec."Shortcut Dimension 2 Code");
                                                                        //SAA3.0 >>
                                                                        SalLineLRec.VALIDATE("Item Category Code", SalesLineLRec."Item Category Code");
                                                                        //SalLineLRec.VALIDATE("Product Group Code", SalesLineLRec."Product Group Code");//B2B
                                                                        SalLineLRec.VALIDATE("VAT Bus. Posting Group", SalesLineLRec."VAT Bus. Posting Group");
                                                                        //SAA3.0 >>
                                                                        IF ItemLLRec.GET(PromoOfferLineLRec."Gift Item No.") THEN;
                                                                        SalLineLRec.VALIDATE("Shortcut Dimension 3 Code", ItemLLRec."Shortcut Dimension 3 Code");
                                                                        SalLineLRec.VALIDATE("No.", PromoOfferLineLRec."Gift Item No.");
                                                                        SalLineLRec.VALIDATE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                        //RKD<<
                                                                        //IF SalesHeader2.GET(SalesLineLRec."Document Type",SalesLineLRec."Document No.") THEN
                                                                        //IF "Direct Sales Order" THEN  B2B
                                                                        IF "Sales Type" = "Sales Type"::Direct then
                                                                            SalLineLRec."Location Code" := SalesLineLRec."Location Code"
                                                                        ELSE
                                                                            //RKD >>
                                                                            SalLineLRec."Location Code" := SalesLineLRec."Location Code";  //removed validation for lekki
                                                                                                                                           //SalLineLRec.VALIDATE("Gift Item Quantity",PromoOfferLineLRec."Promotion Quantity");
                                                                        SalLineLRec.VALIDATE("Gift Item Quantity", PromoGiftQty);//SAA3.0
                                                                        SalLineLRec.VALIDATE("Unit Price", 0);
                                                                        SalLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                        SalLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                                        SalLineLRec."Promo. Offer Line No." := PromoOfferLineLRec."Line No.";
                                                                        SalLineLRec."Gift Item" := TRUE;
                                                                        PromoGrpHeadLRec.SETRANGE("No.", PromoOfferLineLRec."Promo. Schd. Document No.");
                                                                        //GJ_CHIPOS_RKD_191013 >>>>
                                                                        IF "POS Window" THEN
                                                                            PromoGrpHeadLRec.SETRANGE("Retail Promo", TRUE)
                                                                        ELSE
                                                                            PromoGrpHeadLRec.SETRANGE("Retail Promo", FALSE);
                                                                        //GJ_CHIPOS_RKD_191013 <<<<
                                                                        IF PromoGrpHeadLRec.FINDFIRST THEN
                                                                            //IF PromoGrpHeadLRec.GET(PromoOfferLineLRec."Promo. Schd. Document No.") THEN
                                                                            SalLineLRec.VALIDATE("Gen. Bus. Posting Group", PromoGrpHeadLRec."Gen. Bus. Posting Group");
                                                                        SalLineLRec.MODIFY;
                                                                    END;
                                                                UNTIL PromoOfferLineLRec.NEXT = 0;
                                                        END;

                                                    END;
                                                    //  update gift items when the promo group is valid <<
                                                END;
                                            // Checking for Promotion Group is valid for Sales Order Qty. to update free gift items <<
                                            UNTIL NOT OfferValid;
                                        END ELSE
                                            IF PromoGrpHeadLRec."Promo Type" = PromoGrpHeadLRec."Promo Type"::Group THEN BEGIN
                                                // Checking Promo is valid for sales details as a group >>

                                                CLEAR(TotalOrdRemQty);
                                                OfferValid := TRUE;
                                                REPEAT
                                                    CLEAR(PromoExists);
                                                    MinQtyLeft := PromoGrpHeadLRec."Group Quantity";
                                                    TempMinQtyLeft := MinQtyLeft;
                                                    CLEAR(TotalOrdRemQty);
                                                    SalesLineLRec.RESET;
                                                    SalesLineLRec.SETCURRENTKEY(Type, "No.", "Variant Code", "Drop Shipment", "Location Code", "Document Type",
                                                                                "Shipment Date");
                                                    SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                    SalesLineLRec.SETRANGE("Document No.", "No.");
                                                    SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                    SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                    IF SalesLineLRec.FINDSET THEN BEGIN
                                                        REPEAT
                                                            PromoGrpLineLRec.RESET;
                                                            PromoGrpLineLRec.SETRANGE("Document Type", PromoGrpLineLRec."Document Type"::"Promo Group");
                                                            PromoGrpLineLRec.SETRANGE("Document No.", PromoSchdLineLRec."No.");
                                                            PromoGrpLineLRec.SETRANGE(Type, PromoGrpLineLRec.Type::Item);
                                                            //GJ_CHIPOS_RKD_191013 >>>>
                                                            IF "POS Window" THEN
                                                                PromoGrpLineLRec.SETRANGE("Retail Promo", TRUE)
                                                            ELSE
                                                                PromoGrpLineLRec.SETRANGE("Retail Promo", FALSE);
                                                            //GJ_CHIPOS_RKD_191013 <<<<
                                                            PromoGrpLineLRec.SETRANGE("No.", SalesLineLRec."No.");
                                                            IF PromoGrpLineLRec.FINDSET THEN BEGIN
                                                                // Checking Sales Remaining Qty & Changing into Promogroup Item UOM >>
                                                                SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");
                                                                ItemLRec.GET(SalesLineLRec."No.");
                                                                TotalOrdRemQty += (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                                GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpHeadLRec."Group UOM")) * SalesQty;
                                                                TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');

                                                                TotalQtySold := TotalOrdRemQty;//SAA3.0
                                                                                               // Checking Sales Remaining Qty & Changing into Promogroup Item UOM <<
                                                                PromoExists := TRUE;
                                                            END;
                                                        UNTIL (SalesLineLRec.NEXT = 0);

                                                        IF (MinQtyLeft <= 0) THEN
                                                            OfferValid := FALSE;
                                                        IF TotalOrdRemQty < MinQtyLeft THEN
                                                            OfferValid := FALSE;
                                                        MinQtyLeft -= TotalOrdRemQty;

                                                        // To skip loop if no promotion lines exists on sales order >>
                                                        IF NOT PromoExists THEN
                                                            OfferValid := FALSE;
                                                        // To skip loop if no promotion lines exists on sales order <<

                                                        IF OfferValid THEN BEGIN
                                                            SalesLineLRec.RESET;
                                                            SalesLineLRec.SETCURRENTKEY(Type, "No.", "Variant Code", "Drop Shipment", "Location Code", "Document Type",
                                                                                        "Shipment Date");
                                                            SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                            SalesLineLRec.SETRANGE("Document No.", "No.");
                                                            SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                            SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                            IF SalesLineLRec.FINDSET THEN BEGIN
                                                                REPEAT
                                                                    PromoGrpLineLRec.RESET;
                                                                    PromoGrpLineLRec.SETRANGE("Document Type", PromoGrpLineLRec."Document Type"::"Promo Group");
                                                                    PromoGrpLineLRec.SETRANGE("Document No.", PromoSchdLineLRec."No.");
                                                                    PromoGrpLineLRec.SETRANGE(Type, PromoGrpLineLRec.Type::Item);
                                                                    PromoGrpLineLRec.SETRANGE("No.", SalesLineLRec."No.");
                                                                    //GJ_CHIPOS_RKD_191013 >>>>
                                                                    IF "POS Window" THEN
                                                                        PromoGrpLineLRec.SETRANGE("Retail Promo", TRUE)
                                                                    ELSE
                                                                        PromoGrpLineLRec.SETRANGE("Retail Promo", FALSE);
                                                                    //GJ_CHIPOS_RKD_191013 <<<<
                                                                    IF PromoGrpLineLRec.FINDSET THEN BEGIN
                                                                        // Checking Sales Remaining Qty & Changing into Promogroup Item UOM >>
                                                                        SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");
                                                                        ItemLRec.GET(SalesLineLRec."No.");
                                                                        TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                                        GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpHeadLRec."Group UOM")) * SalesQty;
                                                                        TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                                                        // Checking Sales Remaining Qty & Changing into Promogroup Item UOM <<

                                                                        IF TotalOrdRemQty <= TempMinQtyLeft THEN BEGIN
                                                                            TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpHeadLRec."Group UOM") /
                                                                                               GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                                               * TotalOrdRemQty;
                                                                            TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                                                        END ELSE BEGIN
                                                                            TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpHeadLRec."Group UOM") /
                                                                                               GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                                               * TempMinQtyLeft;
                                                                            TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                                                        END;

                                                                        IF TotalOrdRemQty <> 0 THEN BEGIN
                                                                            SalesLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                            SalesLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                                        END;
                                                                        SalesLineLRec."Promo Qty. Util." += TotalOrdRemQty;
                                                                        SalesLineLRec.MODIFY;

                                                                        TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                                        GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpHeadLRec."Group UOM")) * TotalOrdRemQty;

                                                                        TempMinQtyLeft -= TotalOrdRemQty;
                                                                    END;
                                                                UNTIL (SalesLineLRec.NEXT = 0);

                                                                //To filter Promotion Type
                                                                PromoOfferLineLGRec.RESET;
                                                                PromoOfferLineLGRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                                PromoOfferLineLGRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                                PromoOfferLineLGRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                                //PromoOfferLineLGRec.SETFILTER("Min. Quantity",'<=%1',TotalOrdRemQty);
                                                                PromoOfferLineLGRec.SETRANGE(Active, TRUE);
                                                                IF PromoOfferLineLGRec.FINDFIRST THEN
                                                                    IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::"Fixed Quantity" THEN BEGIN
                                                                        PromoOfferLineLRec.RESET;
                                                                        PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                                        PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                                        PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                                        PromoOfferLineLRec.SETFILTER("Min. Quantity", '<=%1', TotalQtySold); //TotalOrdRemQty);
                                                                        PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                                                        PromoOfferLineLRec.ASCENDING(TRUE);
                                                                        IF PromoOfferLineLRec.FINDLAST THEN BEGIN

                                                                            SalLineLRec.RESET;
                                                                            SalLineLRec.SETRANGE("Document Type", "Document Type");
                                                                            SalLineLRec.SETRANGE("Document No.", "No.");
                                                                            SalLineLRec.SETRANGE(Type, SalLineLRec.Type);
                                                                            SalLineLRec.SETFILTER("No.", PromoOfferLineLRec."Gift Item No.");
                                                                            SalLineLRec.SETRANGE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                            SalLineLRec.SETRANGE("Gift Item", TRUE);
                                                                            IF SalLineLRec.FINDFIRST THEN BEGIN
                                                                                //SalLineLRec."Gift Item Quantity" += PromoOfferLineLRec."Promotion Quantity";
                                                                                //SalLineLRec.VALIDATE("Gift Item Quantity");
                                                                                //SalLineLRec.VALIDATE("Unit Price",0);
                                                                                //SalLineLRec.MODIFY(TRUE);
                                                                            END ELSE BEGIN
                                                                                SalLineLRec.INIT;
                                                                                SalLineLRec."Document Type" := SalesLineLRec."Document Type";
                                                                                SalLineLRec."Document No." := SalesLineLRec."Document No.";
                                                                                ReturnSalesLineNo(SalesLineLRec, PromoSchdLineLRec."Document No.", PromoSchdLineLRec."Line No.");
                                                                                SalLineLRec."Line No." := (LineNoGVar + 100);
                                                                                //SalLineLRec.VALIDATE("Sell-to Customer No.",SalesLineLRec."Sell-to Customer No.");
                                                                                //SalLineLRec."Shortcut Dimension 1 Code":=SalesLineLRec."Shortcut Dimension 1 Code";
                                                                                //SalLineLRec."Shortcut Dimension 2 Code":=SalesLineLRec."Shortcut Dimension 2 Code";

                                                                                SalLineLRec.INSERT;
                                                                                SalLineLRec.VALIDATE("Sell-to Customer No.", SalesLineLRec."Sell-to Customer No.");
                                                                                SalLineLRec.VALIDATE(Type, SalesLineLRec.Type);
                                                                                IF ItemLLRec.GET(PromoOfferLineLRec."Gift Item No.") THEN;
                                                                                //SalLineLRec.VALIDATE("Shortcut Dimension 1 Code",SalesLineLRec."Shortcut Dimension 1 Code");
                                                                                //SalLineLRec.VALIDATE("Shortcut Dimension 2 Code",SalesLineLRec."Shortcut Dimension 2 Code");
                                                                                //SalLineLRec.VALIDATE("Shortcut Dimension 3 Code",ItemLLRec."Shortcut Dimension 3 Code");
                                                                                //SAA3.0 >>
                                                                                SalLineLRec.VALIDATE("Item Category Code", SalesLineLRec."Item Category Code");
                                                                                //SalLineLRec.VALIDATE("Product Group Code", SalesLineLRec."Product Group Code");//B2B
                                                                                SalLineLRec.VALIDATE("VAT Bus. Posting Group", SalesLineLRec."VAT Bus. Posting Group");
                                                                                //SAA3.0 >>
                                                                                SalLineLRec.VALIDATE("No.", PromoOfferLineLRec."Gift Item No.");
                                                                                SalLineLRec.VALIDATE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                                //RKD<<
                                                                                //IF SalesHeader2.GET(SalesLineLRec."Document Type",SalesLineLRec."Document No.") THEN
                                                                                //IF "Direct Sales Order" THEN B2B
                                                                                IF "Sales Type" = "Sales Type"::Direct then
                                                                                    SalLineLRec."Location Code" := SalesLineLRec."Location Code"
                                                                                ELSE
                                                                                    //RKD >>
                                                                                    SalLineLRec."Location Code" := SalesLineLRec."Location Code";   //removed validation for lekki
                                                                                SalLineLRec.VALIDATE("Gift Item Quantity", PromoOfferLineLRec."Promotion Quantity");
                                                                                SalLineLRec.VALIDATE("Unit Price", 0);
                                                                                SalLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                                SalLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                                                SalLineLRec."Promo. Offer Line No." := PromoOfferLineLRec."Line No.";
                                                                                SalLineLRec."Gift Item" := TRUE;
                                                                                PromoGrpHeadLRec.SETRANGE("No.", PromoOfferLineLRec."Promo. Schd. Document No.");
                                                                                IF PromoGrpHeadLRec.FINDFIRST THEN BEGIN
                                                                                    //IF PromoGrpHeadLRec.GET(PromoOfferLineLRec."Promo. Schd. Document No.") THEN
                                                                                    SalLineLRec.VALIDATE("Gen. Bus. Posting Group", PromoGrpHeadLRec."Gen. Bus. Posting Group");
                                                                                END;
                                                                                SalLineLRec.MODIFY;
                                                                            END;
                                                                            //UNTIL PromoOfferLineLRec.NEXT = 0;
                                                                        END;
                                                                    END;
                                                                IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::Percentage THEN BEGIN
                                                                    PromoOfferLineLRec.RESET;
                                                                    PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                                    PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                                    PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                                    PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                                                    IF PromoOfferLineLRec.FINDSET THEN
                                                                        REPEAT
                                                                            //Find Percentage and promo gift quantity
                                                                            //MultiplePercentage:= ROUND(((10 * PromoOfferLineLRec."Promotion Quantity")/100)
                                                                            //    * PromoOfferLineLRec."Min. Quantity",1);
                                                                            //PromoGiftQty := ROUND(MultiplePercentage * TotalOrdRemQty / 100,1,'<');
                                                                            //PromoGiftQty := ROUND(PromoOfferLineLRec."Promotion Quantity" * TotalOrdRemQty,1,'<');

                                                                            PromoGiftQty := ROUND(((10 * PromoOfferLineLRec."Promotion Quantity") / 100)
                                                                                  * TotalQtySold, 1);

                                                                            SalLineLRec.RESET;
                                                                            SalLineLRec.SETRANGE("Document Type", "Document Type");
                                                                            SalLineLRec.SETRANGE("Document No.", "No.");
                                                                            SalLineLRec.SETRANGE(Type, SalLineLRec.Type);
                                                                            SalLineLRec.SETFILTER("No.", PromoOfferLineLRec."Gift Item No.");
                                                                            SalLineLRec.SETRANGE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                            SalLineLRec.SETRANGE("Gift Item", TRUE);
                                                                            IF SalLineLRec.FINDFIRST THEN BEGIN
                                                                                //SalLineLRec."Gift Item Quantity" += PromoOfferLineLRec."Promotion Quantity";
                                                                                //SalLineLRec.VALIDATE("Gift Item Quantity");
                                                                                //SalLineLRec.VALIDATE("Unit Price",0);
                                                                                //SalLineLRec.MODIFY(TRUE);
                                                                            END ELSE BEGIN
                                                                                SalLineLRec.INIT;
                                                                                SalLineLRec."Document Type" := SalesLineLRec."Document Type";
                                                                                SalLineLRec."Document No." := SalesLineLRec."Document No.";
                                                                                ReturnSalesLineNo(SalesLineLRec, PromoSchdLineLRec."Document No.", PromoSchdLineLRec."Line No.");
                                                                                SalLineLRec."Line No." := (LineNoGVar + 100);
                                                                                //SalLineLRec.VALIDATE("Sell-to Customer No.",SalesLineLRec."Sell-to Customer No.");
                                                                                //SalLineLRec."Shortcut Dimension 1 Code":=SalesLineLRec."Shortcut Dimension 1 Code";
                                                                                //SalLineLRec."Shortcut Dimension 2 Code":=SalesLineLRec."Shortcut Dimension 2 Code";

                                                                                SalLineLRec.INSERT;
                                                                                SalLineLRec.VALIDATE("Sell-to Customer No.", SalesLineLRec."Sell-to Customer No.");
                                                                                SalLineLRec.VALIDATE(Type, SalesLineLRec.Type);
                                                                                IF ItemLLRec.GET(PromoOfferLineLRec."Gift Item No.") THEN;
                                                                                //SalLineLRec.VALIDATE("Shortcut Dimension 1 Code",SalesLineLRec."Shortcut Dimension 1 Code");
                                                                                //SalLineLRec.VALIDATE("Shortcut Dimension 2 Code",SalesLineLRec."Shortcut Dimension 2 Code");
                                                                                //SalLineLRec.VALIDATE("Shortcut Dimension 3 Code",ItemLLRec."Shortcut Dimension 3 Code");
                                                                                //SAA3.0 >>
                                                                                SalLineLRec.VALIDATE("Item Category Code", SalesLineLRec."Item Category Code");
                                                                                //SalLineLRec.VALIDATE("Product Group Code", SalesLineLRec."Product Group Code");//B2B
                                                                                SalLineLRec.VALIDATE("VAT Bus. Posting Group", SalesLineLRec."VAT Bus. Posting Group");
                                                                                //SAA3.0 >>
                                                                                SalLineLRec.VALIDATE("No.", PromoOfferLineLRec."Gift Item No.");
                                                                                SalLineLRec.VALIDATE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                                //RKD<<
                                                                                //IF SalesHeader2.GET(SalesLineLRec."Document Type",SalesLineLRec."Document No.") THEN
                                                                                //IF "Direct Sales Order" THEN B2B
                                                                                IF "Sales Type" = "Sales Type"::Direct then
                                                                                    SalLineLRec."Location Code" := SalesLineLRec."Location Code"
                                                                                ELSE
                                                                                    //RKD >>
                                                                                    SalLineLRec."Location Code" := SalesLineLRec."Location Code";  //removed validation for lekki
                                                                                                                                                   //SalLineLRec.VALIDATE("Gift Item Quantity",PromoOfferLineLRec."Promotion Quantity");
                                                                                SalLineLRec.VALIDATE("Gift Item Quantity", PromoGiftQty);//SAA3.0
                                                                                SalLineLRec.VALIDATE("Unit Price", 0);
                                                                                SalLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                                SalLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                                                SalLineLRec."Promo. Offer Line No." := PromoOfferLineLRec."Line No.";
                                                                                SalLineLRec."Gift Item" := TRUE;
                                                                                PromoGrpHeadLRec.SETRANGE("No.", PromoOfferLineLRec."Promo. Schd. Document No.");
                                                                                IF PromoGrpHeadLRec.FINDFIRST THEN
                                                                                    //IF PromoGrpHeadLRec.GET(PromoOfferLineLRec."Promo. Schd. Document No.") THEN
                                                                                    SalLineLRec.VALIDATE("Gen. Bus. Posting Group", PromoGrpHeadLRec."Gen. Bus. Posting Group");
                                                                                SalLineLRec.MODIFY;
                                                                            END;
                                                                        UNTIL PromoOfferLineLRec.NEXT = 0;
                                                                END;

                                                            END;
                                                        END;
                                                    END;
                                                UNTIL NOT OfferValid;
                                                // Checking Promo is valid for sales details as a group <<

                                            END;
                                    END;
                                END;
                        END;
                        // Updating the calculated Promotional Gift Qty. to Quantity field in Sales Lines. >>
                        IF AcctLocAllowed THEN BEGIN
                            SalLineLRec.RESET;
                            SalLineLRec.SETRANGE("Document Type", "Document Type");
                            SalLineLRec.SETRANGE("Document No.", "No.");
                            SalLineLRec.SETRANGE("Gift Item", TRUE);
                            IF SalLineLRec.FINDSET THEN
                                REPEAT
                                    SalLineLRec.VALIDATE("Gift Item", FALSE);
                                    //GJ_CHIPOS_RKD_211013 >>>>
                                    SalesHeader.RESET;
                                    SalesHeader.SETRANGE("Document Type", "Document Type");
                                    SalesHeader.SETRANGE("No.", "No.");
                                    SalesHeader.SETRANGE("POS Window", TRUE);
                                    IF SalesHeader.FINDFIRST THEN
                                        SalLineLRec."POS Window" := TRUE;
                                    //GJ_CHIPOS_RKD_211013 <<<<

                                    SalLineLRec.VALIDATE(Quantity, SalLineLRec."Gift Item Quantity");
                                    SalLineLRec.VALIDATE("Unit Price", 0);
                                    SalLineLRec.VALIDATE("Gift Item", TRUE);
                                    //SalLineLRec.MODIFY;//PKONAU4
                                    //PKONAU3>>
                                    IF Valid.CheckQtyValidationsTrueOrFalse(SalLineLRec) THEN
                                        SalLineLRec.SelectGiftItemFlavourLines(SalLineLRec);
                                    //PKONAU3<<
                                    SalLineLRec.MODIFY;//PKONAU4
                                    COMMIT;
                                UNTIL SalLineLRec.NEXT = 0;
                        END;
                    // Updating the Promotional Gift Qty. to Quantity field in Sales Lines. <<
                    UNTIL PromoSchdLineLRec.NEXT = 0;
                end;//else
                    //Message('No Promotions are avaialble for this Customer.');//PK-Prom
                    //end;//Prasanna
            UNTIL PromoSchdLRec.NEXT = 0;
        end;// else
            //Message('No Promotions are avaialble for this Customer.');//PK-Prom
        SalesLine.Reset();
        SalesLine.SetRange("Document Type", "Document Type");
        SalesLine.setrange("Document No.", "No.");
        if SalesLine.FindSet() then
            repeat
                //Fix 07052021>>
                SalesLine."Qty. to Ship" := SalesLine.Quantity - SalesLine."Quantity Shipped";
                SalesLine."Qty. to Ship (Base)" := SalesLine."Quantity (Base)" - SalesLine."Qty. Shipped (Base)";
                SalesLine."Qty. to Invoice" := SalesLine.Quantity - SalesLine."Quantity Invoiced";
                SalesLine."Qty. to Invoice (Base)" := SalesLine."Quantity (Base)" - SalesLine."Qty. Invoiced (Base)";
                //Fix 07052021<<
                SalesLine.Modify();
                SalesLine.SetItemTracking();
            until SalesLine.Next() = 0;

    end;

    procedure CheckPromoRestriction(PromoHdrLRec: Record "Promo Schedule")
    var
        PromoRestrictionLRec: Record "Promo Sched Restrictions";
    begin
        //Below Page is  Promo Schd. restrictions
        WITH PromoHdrLRec DO BEGIN
            IF "Scheme Applicable" = "Scheme Applicable"::Restricted THEN BEGIN
                PromoRestrictionLRec.SETRANGE("Document Type", PromoHdrLRec."Document Type");
                PromoRestrictionLRec.SETRANGE("Promo Schd. Code", PromoHdrLRec."No.");
                PromoRestrictionLRec.SETRANGE("Responsibility Center", "Responsibility Center");
                IF PromoRestrictionLRec.FINDFIRST THEN
                    AcctLocAllowed := TRUE
                ELSE
                    AcctLocAllowed := FALSE;
            END ELSE
                AcctLocAllowed := TRUE;
        END;
    end;

    procedure GetQtyPerUnitOfMeasure(Item: Record Item; UnitOfMeasureCode: Code[10]): Decimal
    begin
        Item.TESTFIELD("No.");
        IF UnitOfMeasureCode IN [Item."Base Unit of Measure", ''] THEN
            EXIT(1);
        IF (Item."No." <> ItemUnitOfMeasure."Item No.") OR
           (UnitOfMeasureCode <> ItemUnitOfMeasure.Code)
        THEN
            IF NOT ItemUnitOfMeasure.GET(Item."No.", UnitOfMeasureCode) THEN
                IF ItemUnitOfMeasure.GET(Item."No.", Item."Base Unit of Measure") THEN
                    IF ItemUnitOfMeasure."Qty. per Unit of Measure" = 1 THEN
                        EXIT(ItemUnitOfMeasure."Qty. per Unit of Measure");

        ItemUnitOfMeasure.GET(Item."No.", UnitOfMeasureCode);
        ItemUnitOfMeasure.TESTFIELD("Qty. per Unit of Measure");
        EXIT(ItemUnitOfMeasure."Qty. per Unit of Measure");
    end;

    procedure ReturnSalesLineNo(VAR SalesLineRecPar: Record "Sales Line"; PromoNoPar: Code[20]; PromoLineNoPar: Integer): Integer
    var
        SalesLineLRec: Record "Sales Line";
    begin
        SalesLineLRec.RESET;
        SalesLineLRec.SETRANGE("Document Type", "Document Type");
        SalesLineLRec.SETRANGE("Document No.", "No.");
        IF PromoNoPar <> '' THEN
            SalesLineLRec.SETRANGE("Promo. No.", PromoNoPar);
        IF PromoLineNoPar <> 0 THEN
            SalesLineLRec.SETRANGE("Promo. Line No.", PromoLineNoPar);
        IF SalesLineLRec.FINDLAST THEN BEGIN
            FindLastLineNo(SalesLineLRec);
        END ELSE
            FindLastLineNo(SalesLineRecPar);
    end;

    procedure FindLastLineNo(SalesLineRecPar: Record "Sales Line")
    var
        LineNoLVar: Integer;
        SalesLineLRec: Record "Sales Line";
    begin
        CLEAR(LineNoGVar);
        LineNoLVar := (SalesLineRecPar."Line No." + 100);
        SalesLineLRec.RESET;
        SalesLineLRec.SETRANGE("Document Type", SalesLineRecPar."Document Type");
        SalesLineLRec.SETRANGE("Document No.", SalesLineRecPar."Document No.");
        SalesLineLRec.SETRANGE("Line No.", LineNoLVar);
        IF SalesLineLRec.FINDFIRST THEN
            FindLastLineNo(SalesLineLRec)
        ELSE BEGIN
            LineNoGVar := SalesLineRecPar."Line No.";
        END;

    end;

    PROCEDURE CheckCrMemoRetReasonValues();
    VAR
        SalesLineLRec: Record "Sales Line";
    BEGIN
        IF "Document Type" = "Document Type"::"Credit Memo" THEN BEGIN
            IF "Cr. Memo Reason Type" = "Cr. Memo Reason Type"::"Return Order" THEN BEGIN
                SalesLineLRec.RESET;
                SalesLineLRec.SETRANGE("Document Type", "Document Type");
                SalesLineLRec.SETRANGE("Document No.", "No.");
                SalesLineLRec.SETFILTER("Return Receipt No.", '=%1', '');
                SalesLineLRec.SETFILTER("Return Receipt Line No.", '=%1', 0);
                IF SalesLineLRec.FINDFIRST THEN
                    ERROR(Text50210, "Document Type", "No.");
            END;
        END;
    END;

    procedure CancelSalesOrder();
    var
        SaleLineLRec: Record "Sales Line";
        ArchiveManagement: Codeunit ArchiveManagement;
        Text50201: Label 'There is nothing to cancel.';
        Text50202: Label 'Do you want to cancel the Sales Order?';
        Text50203: Label 'There is one or more sales line with Shipped Quantity, You can only Short Close the document.';
    begin
        IF NOT ("Order Status" = "Order Status"::Cancelled) THEN BEGIN
            IF NOT SalesLinesExist THEN
                ERROR(Text50201);

            IF NOT ShippedSalesLinesExist THEN BEGIN
                IF NOT CONFIRM(Text50202, FALSE) THEN
                    EXIT;
                ArchiveManagement.ArchSalesDocumentNoConfirm(Rec);

                //check if rebate variable exits and return balance
                if RebateVariableexits() then
                    ReturnRebateBalance();

                SaleLineLRec.RESET;
                SaleLineLRec.SETRANGE("Document Type", "Document Type");
                SaleLineLRec.SETRANGE("Document No.", "No.");
                SaleLineLRec.SETFILTER("Order Status", '=%1', SaleLineLRec."Order Status"::" ");
                IF SaleLineLRec.FINDSET THEN
                    REPEAT
                        SaleLineLRec.VALIDATE(Quantity, 0);
                        SaleLineLRec."Order Status" := SaleLineLRec."Order Status"::Cancelled;
                        SaleLineLRec.MODIFY;
                    UNTIL SaleLineLRec.NEXT = 0;
                "Order Status" := "Order Status"::Cancelled;
                MODIFY;

            END ELSE
                ERROR(Text50203);
        END
    end;

    procedure CheckForApplicationType();
    var
        CustomerEntry: Record "Cust. Ledger Entry";
        ApplyCustEntries: Page "Apply Customer Entries";
        GenJnlApply: codeunit "Gen. Jnl.-Apply";
        CustLedgEntry: record "Cust. Ledger Entry";
        GenJnILine: record "Gen. Journal Line";
        Amt: Decimal;
        CustCrLimitSchdLRec: Record "Cust. Cr. Limit Schedule";
        DetailedCustLedger: Record "Detailed Cust. Ledg. Entry";
        SalesLineRec: Record "Sales Line";
        SalesLineAmountTotal: Decimal;
        CreditLimitAvailable: Boolean;
        PaymentAmt: Decimal;
        TEXT00001: label 'You must apply with either %1 or %2 for a Cash Customer.';
    begin
        CustCrLimitSchdLRec.RESET;
        CustCrLimitSchdLRec.SETRANGE("Customer No.", "Sell-to Customer No.");
        CustCrLimitSchdLRec.SETRANGE("Responsibility Center", "Responsibility Center");
        CustCrLimitSchdLRec.SETRANGE(Status, CustCrLimitSchdLRec.Status::Released);
        CustCrLimitSchdLRec.SETFILTER(CustCrLimitSchdLRec."Customer Type", '%1', CustCrLimitSchdLRec."Customer Type"::"Credit Customer");
        IF CustCrLimitSchdLRec.ISEMPTY THEN BEGIN
            IF ("Applies-to Doc. No." = '') AND ("Applies-to ID" = '') THEN
                ERROR(TEXT00001, FIELDCAPTION("Applies-to Doc. No."), FIELDCAPTION("Applies-to ID"));
        END;
    end;

    Procedure CheckResponsilibilityCentre()
    VAR
        ShiptoAddressLRec: Record "Ship-to Address";
        RespError: Label 'Responsibility Centre in Sales Header and Ship-to Address tables must be same';
    BEGIN
        //SalesHeader.TestField("Ship-to Code");
        TestField("Location Code");
        ShiptoAddressLRec.reset;
        ShiptoAddressLRec.SetRange("Customer No.", "Sell-to Customer No.");
        ShiptoAddressLRec.SetRange(Code, "Ship-to Code");
        IF ShiptoAddressLRec.findfirst then begin
            IF "Responsibility Center" <> ShiptoAddressLRec."Responsibility Center" then
                ERROR(RespError);
        END;
    end;
    //b2bpksalecorr10 Below function is modified
    procedure SendReleaseMails(ToMail: text[100]; Content: Integer)
    var
        Recipients: List of [Text];
        UsersLRec: Record User;
        LocationLRec: Record Location;
        SMTPMail: Codeunit "SMTP Mail";
        //RFCMTDNotificationGo2solveMay2023>>>>>>
        FromMail, salesPerson1Mail, salesPerson2Mail : Text[100];
        //RFCMTDNotificationGo2solveMay2023<<<<<<
        Subject: Text;
        SMTPSetup: Record "SMTP Mail Setup";
        SalLine: Record "Sales Line";
        salesPerson: Record "Salesperson/Purchaser";
        customerRec: Record Customer;
    BEGIN
        /*   //RFCMTDNotificationGo2solveMay2023>>>>>>
          customerRec.SetRange("No.", "Sell-to Customer No.");
          if (customerRec.FindFirst()) then begin
              salesPerson.SetRange(Code, customerRec."Salesperson Code");
              if (salesPerson.FindFirst()) then
                  salesPerson1Mail := salesPerson."E-Mail";
              salesPerson.Reset();
              salesPerson.SetRange(Code, customerRec."Salesperson Code 1");
              if (salesPerson.FindFirst()) then
                  salesPerson2Mail := salesPerson."E-Mail";
          end;
          //RFCMTDNotificationGo2solveMay2023<<<<<<

          Subject := 'Reg: Sales Order' + "No." + 'is released.';
          SMTPSetup.get;
          FromMail := SMTPSetup."User ID";
          Recipients.ADD(ToMail);

          IF LocationLRec.GET("Location Code") then begin
              if LocationLRec."E-Mail2" <> '' then
                  Recipients.ADD(LocationLRec."E-Mail2");
              if LocationLRec."E-Mail3" <> '' then
                  Recipients.ADD(LocationLRec."E-Mail3");
              //RFCMTDNotificationGo2solveMay2023>>>>>>
              if salesPerson1Mail <> '' then
                  Recipients.Add(salesPerson1Mail);
              if salesPerson2Mail <> '' then
                  Recipients.Add(salesPerson2Mail);
              //RFCMTDNotificationGo2solveMay2023<<<<<<
          end;
          SMTPMail.CreateMessage('ERP', FromMail, Recipients, Subject, '', TRUE);

          SmtpMail.AppendBody('Dear All');
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody('Find below details of the items requested:');
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody('Sales Order No: ' + FORMAT("No."));
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody('Location: ' + FORMAT("Location Code"));
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody('Customer No.: ' + FORMAT("Sell-to Customer No." + '-' + "Sell-to Customer Name"));
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody('Document Date: ' + FORMAT("Document Date"));
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody('<table border="1", width="100%">');
          SmtpMail.AppendBody('<tr>');
          SmtpMail.AppendBody('<th>Item No.</th>');
          SmtpMail.AppendBody('<th>Item Description</th>');
          SmtpMail.AppendBody('<th>Quantity Requested</th>');
          SmtpMail.AppendBody('<th>Unit Of Measure</th>');
          SmtpMail.AppendBody('</tr>');
          SalLine.RESET;
          SalLine.SETRANGE("Document Type", "Document Type");
          SalLine.SETRANGE("Document No.", "No.");
          IF SalLine.FINDSET THEN
              REPEAT
                  SmtpMail.AppendBody('<tr>');
                  SmtpMail.AppendBody('<td>' + FORMAT(SalLine."No.") + '</td>');
                  SmtpMail.AppendBody('<td>' + FORMAT(SalLine.Description) + '</td>');
                  SmtpMail.AppendBody('<td>' + FORMAT(SalLine."Quantity") + '</td>');
                  SmtpMail.AppendBody('<td>' + FORMAT(SalLine."Unit of Measure Code") + '</td>');
                  SmtpMail.AppendBody('</tr>');
              UNTIL SalLine.NEXT = 0;
          SmtpMail.AppendBody('</table>');
          SmtpMail.AppendBody('</body>');
          SmtpMail.AppendBody('</html>');
          SmtpMail.AppendBody('<br>');
          if Content = 0 then
              SmtpMail.AppendBody('Order has been approved and will be shipped soon.')
          else
              SmtpMail.AppendBody('Please process the request.');
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody('Regards');
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody(FORMAT('Sales team.'));
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody('<br>');
          SmtpMail.AppendBody('This is a system generated mail. Please do not reply to this email ID.');
          SmtpMail.Send;
          Message('Sale Order Mail Sent'); */
    END;

    procedure GenerateSalesInvReport()
    var
        SalesInv: Record "Sales Invoice Header";
        SalesInv2: Record "Sales Invoice Header";
        SalesReport: Report "Branch Invoice New";
        Salesrecset: Record "Sales & Receivables Setup";
    begin
        //b2bpksalecorr12
        SalesInv.Reset();
        SalesInv.SetRange("Order No.", "No.");
        IF SalesInv.FindLast() then begin
            SalesInv2.Reset();
            SalesInv2.SetRange("No.", SalesInv."No.");
            if SalesInv2.FindFirst() then;
            SalesReport.SetTableView(SalesInv2);
            SalesReport.UseRequestPage(false);
            SalesReport.Run();
            //B2BPKON260521 >>
            if Salesrecset.get() And Salesrecset."Mail On Invoice" then
                SendInvoicemail(SalesInv2);
            //B2BPKON260521 <<
        end;
        //Report.RunModal(REport::"Branch Invoice New", false, true, SalesInv);
    end;

    Procedure UpdatePostSlipNo()
    var
        SalesLineMrec: Record "Sales Line";
        PostedLoaddSlipLine: Record "Posted Loading Slip Line";
        WarehoseShpLin: record "Posted Whse. Shipment Line";
        PosSaleInv: Record "Sales Invoice Line";
        InsrtPSL: Integer;
    begin
        //b2bpksalecorr13
        /*clear(InsrtPSL);
        SalesLineMrec.RESET;
        SalesLineMrec.SetRange("Document Type", "Document Type");
        SalesLineMrec.SetRange("Document No.", "No.");
        IF SalesLineMrec.FINDSET then BEGIN
            repeat
                WarehoseShpLin.Reset();
                WarehoseShpLin.SetRange("Source No.", SalesLineMrec."Document No.");
                WarehoseShpLin.SetRange("Source Line No.", SalesLineMrec."Line No.");
                if WarehoseShpLin.FindFIRST() then begin
                    // Message('All whr. Lines %1...%2', WarehoseShpLin."No.", WarehoseShpLin."Line No.");
                    PosSaleInv.Reset();
                    PosSaleInv.SetRange("Posted Loading Slip No.", WarehoseShpLin."Posted Loading Slip No.");
                    PosSaleInv.SetRange("Posted Loading Slip Line No.", WarehoseShpLin."Posted Loading Slip Line No.");
                    IF Not PosSaleInv.FindFirst() then begin
                        SalesLineMrec.VALIDATE("Posted Loading Slip No.", WarehoseShpLin."Posted Loading Slip No.");
                        SalesLineMrec.VALIDATE("Posted Loading Slip Line No.", WarehoseShpLin."Posted Loading Slip Line No.");
                        SalesLineMrec.Validate("Qty. to Invoice", WarehoseShpLin.Quantity);
                        SalesLineMrec.Modify();
                        InsrtPSL := 1;
                    end;
                end;
            until SalesLineMrec.Next = 0;
            IF InsrtPSL <> 0 then
                Message('Posted Loading Slip No.s and Invoice quantities field updated succesfully.')
            else
                Message('There is nothing to Handle.');
        end;*/
        //b2bpksalecorr13
        clear(InsrtPSL);
        SalesLineMrec.RESET;
        SalesLineMrec.SetRange("Document Type", "Document Type");
        SalesLineMrec.SetRange("Document No.", "No.");
        SalesLineMrec.SetRange(Type, SalesLineMrec.Type::Item);
        IF SalesLineMrec.FINDSET THEN
            SalesLineMrec.ModifyAll("Posted Loading Slip No.", '');
        SalesLineMrec.RESET;
        SalesLineMrec.SetRange("Document Type", "Document Type");
        SalesLineMrec.SetRange("Document No.", "No.");
        IF SalesLineMrec.FINDSET then BEGIN
            repeat
                WarehoseShpLin.Reset();
                WarehoseShpLin.SetRange("Source No.", SalesLineMrec."Document No.");
                WarehoseShpLin.SetRange("Source Line No.", SalesLineMrec."Line No.");
                if WarehoseShpLin.FindFIRST() then begin
                    repeat //B2BPKON200521End
                        IF SalesLineMrec."Posted Loading Slip No." = '' THEN BEGIN //B2BPKON200521End
                            // Message('All whr. Lines %1...%2', WarehoseShpLin."No.", WarehoseShpLin."Line No.");
                            PostedLoaddSlipLine.Reset();
                            PostedLoaddSlipLine.SetRange("No.", WarehoseShpLin."Whse. Shipment No.");
                            PostedLoaddSlipLine.SetRange(Cancelled, False);//PKONJ25.2
                            PostedLoaddSlipLine.SetRange("Document Line No.", WarehoseShpLin."Whse Shipment Line No.");
                            if PostedLoaddSlipLine.FindSet() then
                                repeat
                                    PosSaleInv.Reset();
                                    PosSaleInv.SetRange("Posted Loading Slip No.", PostedLoaddSlipLine."Document No.");
                                    PosSaleInv.SetRange("Posted Loading Slip Line No.", PostedLoaddSlipLine."Line No.");
                                    IF Not PosSaleInv.FindFirst() then begin
                                        SalesLineMrec.VALIDATE("Posted Loading Slip No.", PostedLoaddSlipLine."Document No.");
                                        SalesLineMrec.VALIDATE("Posted Loading Slip Line No.", PostedLoaddSlipLine."Line No.");
                                        SalesLineMrec.Validate("Qty. to Invoice", PostedLoaddSlipLine."Qty. Loading");
                                        SalesLineMrec.Modify();
                                        InsrtPSL := 1;
                                    end;
                                until PostedLoaddSlipLine.Next() = 0;
                        end; //B2BPKON200521End
                    until WarehoseShpLin.Next = 0; //B2BPKON200521End
                end;
            until SalesLineMrec.Next = 0;
            IF InsrtPSL <> 0 then
                Message('Posted Loading Slip No.s and Invoice quantities field updated succesfully.')
            else
                Message('There is nothing to Handle.');
        end;
    end;

    procedure SendInvoicemail(SINvHDr: Record "Sales Invoice Header")
    var
        Recipients: List of [Text];
        UsersLRec: Record User;
        LocationLRec: Record Location;
        SMTPMail: Codeunit "SMTP Mail";
        FromMail: Text[100];
        Subject: Text;
        SMTPSetup: Record "SMTP Mail Setup";
        SalInvLine: Record "Sales Invoice Line";
        Custlv: Record Customer;
    BEGIN
        /* //B2BPKON260521 entire function
        Subject := 'Reg: Sales Invoice ' + SINvHDr."No." + 'is Posted.';
        SMTPSetup.get;
        FromMail := SMTPSetup."User ID";
        IF Custlv.GET(SINvHDr."Sell-to Customer No.") AND (custlv."E-Mail" <> '') then begin
            Recipients.ADD(custlv."E-Mail");
            IF LocationLRec.GET(SINvHDr."Location Code") then begin
                if LocationLRec."E-Mail2" <> '' then
                    Recipients.ADD(LocationLRec."E-Mail2");
                if LocationLRec."E-Mail3" <> '' then
                    Recipients.ADD(LocationLRec."E-Mail3");
            end;
            SMTPMail.CreateMessage('ERP', FromMail, Recipients, Subject, '', TRUE);

            SmtpMail.AppendBody('Dear All');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Find below details of the items invoiced:');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Invoice No: ' + FORMAT(SINvHDr."No."));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Sales Order No: ' + FORMAT(SINvHDr."Order No."));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Location: ' + FORMAT(SINvHDr."Location Code"));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Customer No.: ' + FORMAT(SINvHDr."Sell-to Customer No." + '-' + SINvHDr."Sell-to Customer Name"));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Posting Date: ' + FORMAT(SINvHDr."Posting Date"));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<table border="1", width="100%">');
            SmtpMail.AppendBody('<tr>');
            SmtpMail.AppendBody('<th>Item No.</th>');
            SmtpMail.AppendBody('<th>Item Description</th>');
            SmtpMail.AppendBody('<th>Quantity Invoiced</th>');
            SmtpMail.AppendBody('<th>Unit Of Measure</th>');
            SmtpMail.AppendBody('</tr>');
            SalInvLine.RESET;
            SalInvLine.SETRANGE("Document No.", "No.");
            IF SalInvLine.FINDSET THEN
                REPEAT
                    SmtpMail.AppendBody('<tr>');
                    SmtpMail.AppendBody('<td>' + FORMAT(SalInvLine."No.") + '</td>');
                    SmtpMail.AppendBody('<td>' + FORMAT(SalInvLine.Description) + '</td>');
                    SmtpMail.AppendBody('<td>' + FORMAT(SalInvLine."Quantity") + '</td>');
                    SmtpMail.AppendBody('<td>' + FORMAT(SalInvLine."Unit of Measure Code") + '</td>');
                    SmtpMail.AppendBody('</tr>');
                UNTIL SalInvLine.NEXT = 0;
            SmtpMail.AppendBody('</table>');
            SmtpMail.AppendBody('</body>');
            SmtpMail.AppendBody('</html>');
            SmtpMail.AppendBody('<br>');

            SmtpMail.AppendBody('Invoice Has been Posted.');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('Regards');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody(FORMAT('Sales team.'));
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('<br>');
            SmtpMail.AppendBody('This is a system generated mail. Please do not reply to this email ID.');
            SmtpMail.Send;
            Message('Invoice details mail has been Sent');
        END; */
    end;
    //PKONAU3
    procedure CheckPromotionLines(SalesLineNoPar: Integer) //PKONAU20 whole function
    var
        ItemLRec: Record Item;
        SalesLineLRec: Record "Sales Line";
        SalLineLRec: record "Sales Line";
        MinQtyLeft: Decimal;
        TempMinQtyLeft: Decimal;
        ItemLLRec: Record Item;
        MultiplePercentage: Decimal;
        GiftQty: Decimal;
        PromoGiftQty: Decimal;
        TotalQtySold: Decimal;
        PromoSchdLRec: Record "Promo Schedule";
        PromoSchdLineLRec: Record "Promo Schedule Line";
        SaleRecSet: Record "Sales & Receivables Setup";
        //AcctLocAllowed: Boolean;
        TotalOrdRemQty: Decimal;
        CustLrec: Record Customer;
        SalesLine: Record "Sales Line";
        Valid: Codeunit Validations;
    begin
        /*
        SaleRecSet.Get();
        if not SaleRecSet."Apply Promotions" then
            exit;

        PromoSchdLRec.RESET;
        PromoSchdLRec.SETRANGE("Document Type", PromoSchdLRec."Document Type"::Promo);
        if "Sales Type" = "Sales Type"::Export then
            PromoSchdLRec.SetRange("Export Cust Offers", true)
        else
            PromoSchdLRec.SetRange("Export Cust Offers", false);
        PromoSchdLRec.SETFILTER("Start Date", '<=%1', "Order Date");
        PromoSchdLRec.SETFILTER("End Date", '>=%1', "Order Date");
        PromoSchdLRec.SETRANGE(Status, PromoSchdLRec.Status::Released);
        IF PromoSchdLRec.FINDSET THEN begin
            REPEAT
                PromoSchdLineLRec.RESET;
                PromoSchdLineLRec.SETRANGE("Document Type", PromoSchdLRec."Document Type");
                PromoSchdLineLRec.SETRANGE("Document No.", PromoSchdLRec."No.");
                PromoSchdLineLRec.SETRANGE(Active, TRUE);
                IF "POS Window" THEN
                    PromoSchdLineLRec.SETRANGE("Retail Promo", TRUE)
                ELSE
                    PromoSchdLineLRec.SETRANGE("Retail Promo", FALSE);
                IF PromoSchdLineLRec.FINDSET THEN begin
                    REPEAT
                        CheckPromoRestriction(PromoSchdLRec);
                        IF AcctLocAllowed THEN
                            IF PromoSchdLineLRec.Type = PromoSchdLineLRec.Type::Item THEN BEGIN
                                CLEAR(TotalOrdRemQty);
                                SalesLineLRec.RESET;
                                SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                SalesLineLRec.SETRANGE("Document No.", "No.");
                                SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                SalesLineLRec.SETRANGE("No.", PromoSchdLineLRec."No.");
                                SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                IF SalesLineLRec.FINDSET THEN BEGIN
                                    REPEAT
                                        SalesQty := SalesLineLRec.Quantity;
                                        ItemLRec.GET(SalesLineLRec."No.");
                                        TotalOrdRemQty += (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                          GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code")) * SalesQty;
                                        TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                    UNTIL SalesLineLRec.NEXT = 0;
                                    CLEAR(NoOfferLineExists);
                                    PromoOfferLineLGRec.RESET;
                                    PromoOfferLineLGRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                    PromoOfferLineLGRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                    PromoOfferLineLGRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                    PromoOfferLineLGRec.SETRANGE(Active, TRUE);
                                    IF PromoOfferLineLGRec.FINDFIRST THEN
                                        //Below Page For Promo. Schd. Offer Lines
                                        IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::"Fixed Quantity" THEN BEGIN
                                            PromoOfferLineLRec.RESET;
                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                            PromoOfferLineLRec.SETFILTER("Promotion Type", '%1', PromoOfferLineLRec."Promotion Type"::"Fixed Quantity");
                                            PromoOfferLineLRec.SETFILTER("Min. Quantity", '<=%1', TotalOrdRemQty);
                                            PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                            PromoOfferLineLRec.ASCENDING(TRUE);
                                            IF PromoOfferLineLRec.FINDLAST THEN BEGIN
                                                MinQtyLeft := PromoOfferLineLRec."Min. Quantity";
                                                SalesLineLRec.RESET;
                                                SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                SalesLineLRec.SETRANGE("Document No.", "No.");
                                                SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                SalesLineLRec.SETRANGE("No.", PromoSchdLineLRec."No.");
                                                SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                IF SalesLineLRec.FINDSET THEN
                                                    REPEAT
                                                        SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");
                                                        ItemLRec.GET(SalesLineLRec."No.");
                                                        SalesQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                          GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code"))
                                                                          * SalesQty;
                                                        SalesQty := ROUND(SalesQty, 1.0, '=');
                                                        IF SalesQty > MinQtyLeft THEN
                                                            SalesQty := MinQtyLeft;
                                                        MinQtyLeft -= SalesQty;
                                                        SalesQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code") /
                                                                          GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                          * SalesQty;
                                                        IF SalesQty <> 0 THEN BEGIN
                                                            Error('Order is qualified for promotions. Pls calculate Promotions.');
                                                            //SalesLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                            //SalesLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                        END;
                                                    until SalesLineLRec.Next = 0;
                                            end;
                                        end;
                                end;
                            end;//Prasanna
                    UNTIL PromoSchdLineLRec.NEXT = 0;
                end;
            UNTIL PromoSchdLRec.NEXT = 0;
        end;*/
        //Balu 05092021>>
        SaleRecSet.Get();
        if not SaleRecSet."Apply Promotions" then
            exit;
        //Balu 05092021<<
        TESTFIELD(Status, Status::Open);
        CustLrec.get("Sell-to Customer No.");
        SalesLineLRec.RESET;
        SalesLineLRec.SETRANGE("Document Type", "Document Type");
        SalesLineLRec.SETRANGE("Document No.", "No.");
        IF SalesLineLRec.FINDSET THEN
            REPEAT
                SalesLineLRec.VALIDATE("Gift Item Quantity", 0);
                SalesLineLRec.VALIDATE("Promo Qty. Util.", 0);
                IF SalesLineLRec."Gift Item" THEN
                    SalesLineLRec.VALIDATE("Unit Price", 0);
                SalesLineLRec.MODIFY(TRUE);
            UNTIL SalesLineLRec.NEXT = 0;
        //Below Page is Promo. Schedule
        PromoSchdLRec.RESET;
        PromoSchdLRec.SETRANGE("Document Type", PromoSchdLRec."Document Type"::Promo);
        if "Sales Type" = "Sales Type"::Export then
            PromoSchdLRec.SetRange("Export Cust Offers", true)
        else
            PromoSchdLRec.SetRange("Export Cust Offers", false);
        PromoSchdLRec.SETFILTER("Start Date", '<=%1', "Order Date");
        PromoSchdLRec.SETFILTER("End Date", '>=%1', "Order Date");
        PromoSchdLRec.SETRANGE(Status, PromoSchdLRec.Status::Released);
        //FIX06Apr2021>>
        /*IF "POS Window" THEN
            PromoSchdLRec.SETRANGE("Retail Promo", TRUE)
        ELSE
            PromoSchdLRec.SETRANGE("Retail Promo", FALSE);*/
        //FIX06Apr2021<<
        IF PromoSchdLRec.FINDSET THEN begin
            REPEAT
                //Added Prasanna
                IF SaleRecSet.get and SaleRecSet."Calculate Promo On Cust. Grp." THEN BEGIN
                    IF PromoSchdLRec."Application Type" = PromoSchdLRec."Application Type"::Customer then
                        PromoSchdLRec.SetRange("Customer Filter", "Sell-to Customer No.") else
                        IF PromoSchdLRec."Application Type" = PromoSchdLRec."Application Type"::"Customer Posting Group" then
                            PromoSchdLRec.SetRange("Customer Filter", CustLrec."Customer Posting Group");
                end;
                //IF PromoSchdLRec.FINDSET THEN begin
                //Added Prasanna
                PromoSchdLineLRec.RESET;
                PromoSchdLineLRec.SETRANGE("Document Type", PromoSchdLRec."Document Type");
                PromoSchdLineLRec.SETRANGE("Document No.", PromoSchdLRec."No.");
                PromoSchdLineLRec.SETRANGE(Active, TRUE);
                IF "POS Window" THEN
                    PromoSchdLineLRec.SETRANGE("Retail Promo", TRUE)
                ELSE
                    PromoSchdLineLRec.SETRANGE("Retail Promo", FALSE);
                IF PromoSchdLineLRec.FINDSET THEN begin
                    REPEAT
                        CheckPromoRestriction(PromoSchdLRec);
                        IF AcctLocAllowed THEN BEGIN
                            IF PromoSchdLineLRec.Type = PromoSchdLineLRec.Type::Item THEN BEGIN
                                CLEAR(TotalOrdRemQty);
                                SalesLineLRec.RESET;
                                SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                SalesLineLRec.SETRANGE("Document No.", "No.");
                                SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                SalesLineLRec.SETRANGE("No.", PromoSchdLineLRec."No.");
                                SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                IF SalesLineLRec.FINDSET THEN BEGIN
                                    REPEAT
                                        SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");
                                        ItemLRec.GET(SalesLineLRec."No.");
                                        TotalOrdRemQty += (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                          GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code")) * SalesQty;
                                        TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                    UNTIL SalesLineLRec.NEXT = 0;
                                    REPEAT
                                        CLEAR(NoOfferLineExists);
                                        PromoOfferLineLGRec.RESET;
                                        PromoOfferLineLGRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                        PromoOfferLineLGRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                        PromoOfferLineLGRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                        PromoOfferLineLGRec.SETRANGE(Active, TRUE);
                                        IF PromoOfferLineLGRec.FINDFIRST THEN
                                            //Below Page For Promo. Schd. Offer Lines
                                            IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::"Fixed Quantity" THEN BEGIN
                                                PromoOfferLineLRec.RESET;
                                                PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                PromoOfferLineLRec.SETFILTER("Promotion Type", '%1', PromoOfferLineLRec."Promotion Type"::"Fixed Quantity");
                                                PromoOfferLineLRec.SETFILTER("Min. Quantity", '<=%1', TotalOrdRemQty);
                                                PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                                PromoOfferLineLRec.ASCENDING(TRUE);
                                                IF PromoOfferLineLRec.FINDLAST THEN BEGIN
                                                    MinQtyLeft := PromoOfferLineLRec."Min. Quantity";
                                                    SalesLineLRec.RESET;
                                                    SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                    SalesLineLRec.SETRANGE("Document No.", "No.");
                                                    SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                    SalesLineLRec.SETRANGE("No.", PromoSchdLineLRec."No.");
                                                    SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                    IF SalesLineLRec.FINDSET THEN
                                                        REPEAT
                                                            SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");
                                                            ItemLRec.GET(SalesLineLRec."No.");
                                                            SalesQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                              GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code"))
                                                                              * SalesQty;
                                                            SalesQty := ROUND(SalesQty, 1.0, '=');
                                                            IF SalesQty > MinQtyLeft THEN
                                                                SalesQty := MinQtyLeft;
                                                            MinQtyLeft -= SalesQty;
                                                            SalesQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code") /
                                                                              GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                              * SalesQty;
                                                            IF SalesQty <> 0 THEN BEGIN
                                                                SalesLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                SalesLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                            END;
                                                            SalesLineLRec."Promo Qty. Util." += SalesQty;
                                                            SalesLineLRec.MODIFY;
                                                        UNTIL SalesLineLRec.NEXT = 0;
                                                    // Updating the Promotion Qty Utilized <<
                                                    SalLineLRec.RESET;
                                                    SalLineLRec.SETRANGE("Document Type", "Document Type");
                                                    SalLineLRec.SETRANGE("Document No.", "No.");
                                                    SalLineLRec.SETRANGE(Type, SalLineLRec.Type::Item);
                                                    SalLineLRec.SETFILTER("No.", PromoOfferLineLRec."Gift Item No.");
                                                    SalLineLRec.SETRANGE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                    SalLineLRec.SETRANGE("Gift Item", TRUE);
                                                    IF SalLineLRec.FINDFIRST THEN BEGIN
                                                        //SalLineLRec."Gift Item Quantity" += PromoOfferLineLRec."Promotion Quantity";
                                                        //SalLineLRec."Gift Item Quantity" := PromoOfferLineLRec."Promotion Quantity";
                                                        //SalLineLRec.VALIDATE("Gift Item Quantity");
                                                        //SalLineLRec.VALIDATE("Unit Price",0);
                                                        //SalLineLRec.MODIFY(TRUE);
                                                    END ELSE BEGIN
                                                        SalLineLRec.INIT;
                                                        SalLineLRec."Document Type" := SalesLineLRec."Document Type";
                                                        SalLineLRec."Document No." := SalesLineLRec."Document No.";
                                                        ReturnSalesLineNo(SalesLineLRec, PromoSchdLineLRec."Document No.", PromoSchdLineLRec."Line No.");
                                                        SalLineLRec."Line No." := (LineNoGVar + 100);
                                                        //SalLineLRec.VALIDATE("Sell-to Customer No.",SalesLineLRec."Sell-to Customer No.");
                                                        IF "POS Window" THEN
                                                            SalLineLRec.Validate("POS Window", true);
                                                        SalLineLRec.VALIDATE("Sell-to Customer No.", SalesLineLRec."Sell-to Customer No.");
                                                        SalLineLRec.Type := SalesLineLRec.Type;
                                                        //SAA3.0 >>
                                                        //SalLineLRec."Shortcut Dimension 1 Code":=SalesLineLRec."Shortcut Dimension 1 Code";
                                                        //SalLineLRec."Shortcut Dimension 2 Code":=SalesLineLRec."Shortcut Dimension 2 Code";
                                                        //SalLineLRec.VALIDATE("Item Category Code",SalesLineLRec."Item Category Code");
                                                        //SalLineLRec.VALIDATE("Product Group Code", SalesLineLRec."Product Group Code");B2B
                                                        SalLineLRec.VALIDATE("VAT Bus. Posting Group", SalesLineLRec."VAT Bus. Posting Group");
                                                        SalLineLRec.INSERT;
                                                        SalLineLRec.VALIDATE("Item Category Code", SalesLineLRec."Item Category Code");
                                                        SalLineLRec.VALIDATE(Type, SalesLineLRec.Type);
                                                        //SalLineLRec.VALIDATE("Shortcut Dimension 1 Code",SalesLineLRec."Shortcut Dimension 1 Code");
                                                        //SalLineLRec.VALIDATE("Shortcut Dimension 2 Code",SalesLineLRec."Shortcut Dimension 2 Code");
                                                        //SAA3.0 >>
                                                        //GJ_CHIPOS_RKD_221013 >>>>

                                                        IF "POS Window" THEN
                                                            SalLineLRec.VALIDATE("POS Window", TRUE);
                                                        //GJ_CHIPOS_RKD_221013 <<<<

                                                        IF ItemLLRec.GET(PromoOfferLineLRec."Gift Item No.") THEN;
                                                        SalLineLRec.VALIDATE("Item Category Code", ItemLLRec."Item Category Code");
                                                        //SalLineLRec.VALIDATE("Product Group Code", ItemLLRec."Product Group Code");//B2B
                                                        SalLineLRec.VALIDATE("Shortcut Dimension 3 Code", ItemLLRec."Shortcut Dimension 3 Code");
                                                        SalLineLRec.VALIDATE("No.", PromoOfferLineLRec."Gift Item No.");
                                                        SalLineLRec.VALIDATE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");

                                                        //RKD >> for Promo Items for Direct Sales order
                                                        //IF SalesHeader2.GET(SalesLineLRec."Document Type",SalesLineLRec."Document No.") THEN
                                                        //IF "Direct Sales Order" THEN B2B
                                                        IF "Sales Type" = "Sales Type"::Direct THEN
                                                            SalLineLRec."Location Code" := SalesLineLRec."Location Code"
                                                        ELSE
                                                            //RKD<<
                                                            SalLineLRec."Location Code" := SalesLineLRec."Location Code"; //Validation removed for Lekki

                                                        SalLineLRec.VALIDATE("Gift Item Quantity", PromoOfferLineLRec."Promotion Quantity");
                                                        SalLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                        SalLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                        SalLineLRec."Promo. Offer Line No." := PromoOfferLineLRec."Line No.";
                                                        SalLineLRec.VALIDATE("Unit Price", 0);
                                                        PromoGrpHeadLRec.SETRANGE("No.", PromoOfferLineLRec."Promo. Schd. Document No.");
                                                        IF PromoGrpHeadLRec.FINDFIRST THEN
                                                            //IF PromoGrpHeadLRec.GET(PromoOfferLineLRec."Promo. Schd. Document No.") THEN
                                                            SalLineLRec.VALIDATE("Gen. Bus. Posting Group", PromoGrpHeadLRec."Gen. Bus. Posting Group");
                                                        SalLineLRec."Gift Item" := TRUE;
                                                        SalLineLRec.MODIFY;
                                                    END;
                                                    TotalOrdRemQty -= PromoOfferLineLRec."Min. Quantity";
                                                END ELSE
                                                    NoOfferLineExists := TRUE;
                                            END;
                                        //for Percentage Offers
                                        IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::Percentage THEN BEGIN
                                            PromoOfferLineLRec.RESET;
                                            //PromoOfferLineLRec.SETCURRENTKEY("Min. Quantity");
                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");

                                            //PromoOfferLineLRec.SETFILTER("Min. Quantity",'<=%1',TotalOrdRemQty);
                                            PromoOfferLineLRec.SETFILTER("Promotion Type", '%1', PromoOfferLineLRec."Promotion Type"::Percentage);
                                            PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                            PromoOfferLineLRec.ASCENDING(TRUE);
                                            IF PromoOfferLineLRec.FINDFIRST THEN BEGIN
                                                // Updating the Promotion Qty Utilized >>
                                                MinQtyLeft := PromoOfferLineLRec."Min. Quantity";
                                                MultiplePercentage := ROUND(((100 * PromoOfferLineLRec."Promotion Quantity")
                                                  / PromoOfferLineLRec."Min. Quantity"), 1);
                                                PromoGiftQty := ROUND(MultiplePercentage * TotalOrdRemQty / 100, 1, '<');

                                                SalesLineLRec.RESET;
                                                SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                SalesLineLRec.SETRANGE("Document No.", "No.");
                                                SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                SalesLineLRec.SETRANGE("No.", PromoSchdLineLRec."No.");
                                                SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                IF SalesLineLRec.FINDSET THEN
                                                    REPEAT

                                                        SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");

                                                        ItemLRec.GET(SalesLineLRec."No.");
                                                        //SalesQty := (UOMMgt.GetQtyPerUnitOfMeasure(ItemLRec,SalesLineLRec."Unit of Measure Code")/
                                                        //                UOMMgt.GetQtyPerUnitOfMeasure(ItemLRec,PromoGrpLineLRec."Unit of Measure Code"))
                                                        //               * SalesQty;  //SAA3.0
                                                        SalesQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                          GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code"))
                                                                          * SalesQty;

                                                        SalesQty := ROUND(SalesQty, 1.0, '=');


                                                        IF SalesQty > MinQtyLeft THEN
                                                            SalesQty := MinQtyLeft;

                                                        MinQtyLeft -= SalesQty;

                                                        //SalesQty := (UOMMgt.GetQtyPerUnitOfMeasure(ItemLRec,PromoGrpLineLRec."Unit of Measure Code") /
                                                        //                 UOMMgt.GetQtyPerUnitOfMeasure(ItemLRec,SalesLineLRec."Unit of Measure Code"))
                                                        //               * SalesQty; //SAA3.0
                                                        SalesQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoSchdLineLRec."Unit of Measure Code") /
                                                                          GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                          * SalesQty;

                                                        IF SalesQty <> 0 THEN BEGIN
                                                            SalesLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                            SalesLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                        END;
                                                        SalesLineLRec."Promo Qty. Util." += SalesQty;
                                                        SalesLineLRec.MODIFY;
                                                    UNTIL SalesLineLRec.NEXT = 0;
                                                // Updating the Promotion Qty Utilized <<

                                                SalLineLRec.RESET;
                                                SalLineLRec.SETRANGE("Document Type", "Document Type");
                                                SalLineLRec.SETRANGE("Document No.", "No.");
                                                SalLineLRec.SETRANGE(Type, SalLineLRec.Type::Item);
                                                SalLineLRec.SETFILTER("No.", PromoOfferLineLRec."Gift Item No.");
                                                SalLineLRec.SETRANGE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                SalLineLRec.SETRANGE("Gift Item", TRUE);
                                                IF SalLineLRec.FINDFIRST THEN BEGIN
                                                    //SalLineLRec."Gift Item Quantity" += PromoOfferLineLRec."Promotion Quantity";
                                                    //SalLineLRec."Gift Item Quantity" := PromoOfferLineLRec."Promotion Quantity";
                                                    //SalLineLRec.VALIDATE("Gift Item Quantity");
                                                    //SalLineLRec.VALIDATE("Unit Price",0);
                                                    //SalLineLRec.MODIFY(TRUE);
                                                END ELSE BEGIN
                                                    SalLineLRec.INIT;
                                                    SalLineLRec."Document Type" := SalesLineLRec."Document Type";
                                                    SalLineLRec."Document No." := SalesLineLRec."Document No.";
                                                    ReturnSalesLineNo(SalesLineLRec, PromoSchdLineLRec."Document No.", PromoSchdLineLRec."Line No.");
                                                    SalLineLRec."Line No." := (LineNoGVar + 100);
                                                    //SalLineLRec.VALIDATE("Sell-to Customer No.",SalesLineLRec."Sell-to Customer No.");
                                                    //GJ_CHIPOS_RKD_221013 >>>>
                                                    IF "POS Window" THEN
                                                        SalLineLRec.VALIDATE("POS Window", TRUE);
                                                    //GJ_CHIPOS_RKD_221013 <<<<

                                                    SalLineLRec.VALIDATE("Sell-to Customer No.", SalesLineLRec."Sell-to Customer No.");
                                                    SalLineLRec.Type := SalesLineLRec.Type;
                                                    //SAA3.0 >>
                                                    SalLineLRec.VALIDATE("Item Category Code", SalesLineLRec."Item Category Code");
                                                    //SalLineLRec.VALIDATE("Product Group Code", SalesLineLRec."Product Group Code");//b2b
                                                    SalLineLRec.VALIDATE("VAT Bus. Posting Group", SalesLineLRec."VAT Bus. Posting Group");
                                                    //SalLineLRec."Shortcut Dimension 1 Code":=SalesLineLRec."Shortcut Dimension 1 Code";
                                                    //SalLineLRec."Shortcut Dimension 2 Code":=SalesLineLRec."Shortcut Dimension 2 Code";

                                                    SalLineLRec.INSERT;

                                                    SalLineLRec.VALIDATE(Type, SalesLineLRec.Type);
                                                    //SalLineLRec.VALIDATE("Shortcut Dimension 1 Code",SalesLineLRec."Shortcut Dimension 1 Code");
                                                    //SalLineLRec.VALIDATE("Shortcut Dimension 2 Code",SalesLineLRec."Shortcut Dimension 2 Code");

                                                    //SAA3.0 >>
                                                    //GJ_CHIPOS_RKD_221013 >>>>
                                                    IF "POS Window" THEN
                                                        SalLineLRec.VALIDATE("POS Window", TRUE);
                                                    //GJ_CHIPOS_RKD_221013 <<<<

                                                    IF ItemLLRec.GET(PromoOfferLineLRec."Gift Item No.") THEN;
                                                    SalLineLRec.VALIDATE("Item Category Code", ItemLLRec."Item Category Code");
                                                    // SalLineLRec.VALIDATE("Product Group Code", ItemLLRec."Product Group Code");//b2b

                                                    SalLineLRec.VALIDATE("Shortcut Dimension 3 Code", ItemLLRec."Shortcut Dimension 3 Code");
                                                    SalLineLRec.VALIDATE("No.", PromoOfferLineLRec."Gift Item No.");
                                                    SalLineLRec.VALIDATE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                    //RKD <<
                                                    //IF SalesHeader2.GET(SalesLineLRec."Document Type",SalesLineLRec."Document No.") THEN
                                                    //IF "Direct Sales Order" THEN @B2B
                                                    IF "Sales Type" = "Sales Type"::Direct then
                                                        SalLineLRec."Location Code" := SalesLineLRec."Location Code"
                                                    ELSE
                                                        //RKD >>
                                                        SalLineLRec."Location Code" := SalesLineLRec."Location Code";  //validation removed for lekki

                                                    //SalLineLRec.VALIDATE("Gift Item Quantity",PromoOfferLineLRec."Promotion Quantity");
                                                    SalLineLRec.VALIDATE("Gift Item Quantity", PromoGiftQty);
                                                    SalLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                    SalLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                    SalLineLRec."Promo. Offer Line No." := PromoOfferLineLRec."Line No.";
                                                    SalLineLRec.VALIDATE("Unit Price", 0);
                                                    PromoGrpHeadLRec.SETRANGE("No.", PromoOfferLineLRec."Promo. Schd. Document No.");
                                                    IF PromoGrpHeadLRec.FINDFIRST THEN
                                                        //IF PromoGrpHeadLRec.GET(PromoOfferLineLRec."Promo. Schd. Document No.") THEN
                                                        SalLineLRec.VALIDATE("Gen. Bus. Posting Group", PromoGrpHeadLRec."Gen. Bus. Posting Group");
                                                    SalLineLRec."Gift Item" := TRUE;
                                                    SalLineLRec.MODIFY;
                                                END;
                                                TotalOrdRemQty -= PromoOfferLineLRec."Min. Quantity";
                                            END ELSE
                                                NoOfferLineExists := TRUE;
                                        END;

                                    UNTIL (TotalOrdRemQty <= 0) OR NoOfferLineExists;
                                    // Checking for Offer Lines with respective offer quantity <<
                                END;
                            END ELSE
                                IF PromoSchdLineLRec.Type = PromoSchdLineLRec.Type::"Promo Group" THEN BEGIN
                                    PromoGrpHeadLRec.RESET;
                                    PromoGrpHeadLRec.SETRANGE("Document Type", PromoGrpHeadLRec."Document Type"::"Promo Group");
                                    PromoGrpHeadLRec.SETRANGE("No.", PromoSchdLineLRec."No.");
                                    //GJ_CHIPOS_RKD_191013 >>>>
                                    IF "POS Window" THEN
                                        PromoGrpHeadLRec.SETRANGE("Retail Promo", TRUE)
                                    ELSE
                                        PromoGrpHeadLRec.SETRANGE("Retail Promo", FALSE);
                                    //GJ_CHIPOS_RKD_191013 <<<<
                                    IF PromoGrpHeadLRec.FINDFIRST THEN BEGIN
                                        IF PromoGrpHeadLRec."Promo Type" = PromoGrpHeadLRec."Promo Type"::" " THEN BEGIN
                                            REPEAT
                                                CLEAR(PromoExists);
                                                OfferValid := TRUE;
                                                // Checking for Promotion Group is valid for Sales Order Qty. to update free gift items >>
                                                PromoGrpLineLRec.RESET;
                                                PromoGrpLineLRec.SETRANGE("Document Type", PromoGrpLineLRec."Document Type"::"Promo Group");
                                                PromoGrpLineLRec.SETRANGE("Document No.", PromoSchdLineLRec."No.");
                                                //GJ_CHIPOS_RKD_191013 >>>>
                                                IF "POS Window" THEN
                                                    PromoGrpLineLRec.SETRANGE("Retail Promo", TRUE)
                                                ELSE
                                                    PromoGrpLineLRec.SETRANGE("Retail Promo", FALSE);
                                                //GJ_CHIPOS_RKD_191013 <<<<
                                                IF PromoGrpLineLRec.FINDSET THEN BEGIN
                                                    REPEAT
                                                        SalesLineLRec.RESET;
                                                        SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                        SalesLineLRec.SETRANGE("Document No.", "No.");
                                                        SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                        SalesLineLRec.SETRANGE("No.", PromoGrpLineLRec."No.");
                                                        SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                        IF SalesLineLRec.FINDSET THEN BEGIN
                                                            CLEAR(TotalOrdRemQty);
                                                            CheckPromoRestriction(PromoSchdLRec);//SAA3.0
                                                            REPEAT
                                                                // Checking Sales Remaining Qty & Changing into Promogroup Item UOM >>
                                                                SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");

                                                                ItemLRec.GET(SalesLineLRec."No.");
                                                                TotalOrdRemQty += (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpLineLRec."Unit of Measure Code")) * SalesQty;
                                                            // Checking Sales Remaining Qty & Changing into Promogroup Item UOM <<
                                                            UNTIL SalesLineLRec.NEXT = 0;
                                                            TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');

                                                            IF TotalOrdRemQty <> 0 THEN BEGIN
                                                                IF (PromoGrpLineLRec."Minimum Qty." = 0) THEN
                                                                    OfferValid := FALSE;
                                                            END;

                                                            IF TotalOrdRemQty < PromoGrpLineLRec."Minimum Qty." THEN
                                                                OfferValid := FALSE;

                                                            PromoExists := TRUE;
                                                        END ELSE
                                                            OfferValid := FALSE;

                                                    UNTIL (PromoGrpLineLRec.NEXT = 0) OR NOT OfferValid;

                                                    // To skip loop if no promotion lines exists on sales order >>
                                                    IF NOT PromoExists THEN
                                                        OfferValid := FALSE;
                                                    // To skip loop if no promotion lines exists on sales order <<

                                                    //  update gift items when the promo group is valid >>

                                                    IF OfferValid THEN BEGIN
                                                        PromoGrpLineLRec.RESET;
                                                        PromoGrpLineLRec.SETRANGE("Document Type", PromoGrpLineLRec."Document Type"::"Promo Group");
                                                        PromoGrpLineLRec.SETRANGE("Document No.", PromoSchdLineLRec."No.");
                                                        //GJ_CHIPOS_RKD_191013 >>>>
                                                        IF "POS Window" THEN
                                                            PromoGrpLineLRec.SETRANGE("Retail Promo", TRUE)
                                                        ELSE
                                                            PromoGrpLineLRec.SETRANGE("Retail Promo", FALSE);
                                                        //GJ_CHIPOS_RKD_191013 <<<<
                                                        IF PromoGrpLineLRec.FINDSET THEN
                                                            REPEAT
                                                                MinQtyLeft := PromoGrpLineLRec."Minimum Qty.";
                                                                SalesLineLRec.RESET;
                                                                SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                                SalesLineLRec.SETRANGE("Document No.", "No.");
                                                                SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                                SalesLineLRec.SETRANGE("No.", PromoGrpLineLRec."No.");
                                                                SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                                IF SalesLineLRec.FINDSET THEN
                                                                    REPEAT
                                                                        SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");

                                                                        ItemLRec.GET(SalesLineLRec."No.");
                                                                        TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                                          GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpLineLRec."Unit of Measure Code"))
                                                                                          * SalesQty;
                                                                        TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');


                                                                        IF TotalOrdRemQty <= MinQtyLeft THEN BEGIN
                                                                            TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpLineLRec."Unit of Measure Code") /
                                                                                               GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                                               * TotalOrdRemQty;
                                                                            TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                                                        END ELSE BEGIN
                                                                            TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpLineLRec."Unit of Measure Code") /
                                                                                               GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                                               * MinQtyLeft;
                                                                            TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                                                        END;
                                                                        IF TotalOrdRemQty <> 0 THEN BEGIN
                                                                            SalesLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                            SalesLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                                        END;
                                                                        SalesLineLRec."Promo Qty. Util." += TotalOrdRemQty;
                                                                        SalesLineLRec.MODIFY;

                                                                        TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                                          GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpLineLRec."Unit of Measure Code"))
                                                                                          * TotalOrdRemQty;
                                                                        TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');

                                                                        MinQtyLeft -= TotalOrdRemQty;
                                                                    UNTIL SalesLineLRec.NEXT = 0;
                                                            UNTIL PromoGrpLineLRec.NEXT = 0;
                                                        //To filter Promotion Type
                                                        PromoOfferLineLGRec.RESET;
                                                        PromoOfferLineLGRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                        PromoOfferLineLGRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                        PromoOfferLineLGRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                        //PromoOfferLineLGRec.SETFILTER("Min. Quantity",'<=%1',TotalOrdRemQty);
                                                        PromoOfferLineLGRec.SETRANGE(Active, TRUE);
                                                        IF PromoOfferLineLGRec.FINDFIRST THEN
                                                            IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::"Fixed Quantity" THEN BEGIN
                                                                PromoOfferLineLRec.RESET;
                                                                PromoOfferLineLRec.SETCURRENTKEY("Min. Quantity");
                                                                PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                                PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                                PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                                PromoOfferLineLRec.SETFILTER("Min. Quantity", '<=%1', TotalOrdRemQty);
                                                                PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                                                PromoOfferLineLRec.ASCENDING(TRUE);
                                                                IF PromoOfferLineLRec.FINDLAST THEN
                                                                    SalLineLRec.RESET;
                                                                SalLineLRec.SETRANGE("Document Type", "Document Type");
                                                                SalLineLRec.SETRANGE("Document No.", "No.");
                                                                SalLineLRec.SETRANGE(Type, SalLineLRec.Type::Item);
                                                                SalLineLRec.SETFILTER("No.", PromoOfferLineLRec."Gift Item No.");
                                                                SalLineLRec.SETRANGE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                SalLineLRec.SETRANGE("Gift Item", TRUE);
                                                                IF SalLineLRec.FINDFIRST THEN BEGIN
                                                                    //SalLineLRec."Gift Item Quantity" += PromoOfferLineLRec."Promotion Quantity";
                                                                    //SalLineLRec.VALIDATE("Gift Item Quantity");
                                                                    //SalLineLRec.VALIDATE("Unit Price",0);
                                                                    //SalLineLRec.MODIFY(TRUE);
                                                                END ELSE BEGIN
                                                                    SalLineLRec.INIT;
                                                                    SalLineLRec."Document Type" := SalesLineLRec."Document Type";
                                                                    SalLineLRec."Document No." := SalesLineLRec."Document No.";
                                                                    ReturnSalesLineNo(SalesLineLRec, PromoSchdLineLRec."Document No.", PromoSchdLineLRec."Line No.");
                                                                    SalLineLRec."Line No." := (LineNoGVar + 100);
                                                                    //SalLineLRec.VALIDATE("Sell-to Customer No.",SalesLineLRec."Sell-to Customer No.");
                                                                    //SalLineLRec."Shortcut Dimension 1 Code":=SalesLineLRec."Shortcut Dimension 1 Code";
                                                                    //SalLineLRec."Shortcut Dimension 2 Code":=SalesLineLRec."Shortcut Dimension 2 Code";
                                                                    //GJ_CHIPOS_RKD_221013 >>>>
                                                                    IF "POS Window" THEN
                                                                        SalLineLRec.VALIDATE("POS Window", TRUE);
                                                                    //GJ_CHIPOS_RKD_221013 <<<<

                                                                    SalLineLRec.INSERT;
                                                                    SalLineLRec.VALIDATE("Sell-to Customer No.", SalesLineLRec."Sell-to Customer No.");
                                                                    SalLineLRec.VALIDATE(Type, SalesLineLRec.Type);
                                                                    //GJ_CHIPOS_RKD_221013 >>>>
                                                                    IF "POS Window" THEN
                                                                        SalLineLRec.VALIDATE("POS Window", TRUE);
                                                                    //GJ_CHIPOS_RKD_221013 <<<<

                                                                    //SalLineLRec.VALIDATE("Shortcut Dimension 1 Code",SalesLineLRec."Shortcut Dimension 1 Code");
                                                                    //SalLineLRec.VALIDATE("Shortcut Dimension 2 Code",SalesLineLRec."Shortcut Dimension 2 Code");
                                                                    //SAA3.0 >>
                                                                    SalLineLRec.VALIDATE("Item Category Code", SalesLineLRec."Item Category Code");
                                                                    //SalLineLRec.VALIDATE("Product Group Code", SalesLineLRec."Product Group Code");//B2B
                                                                    SalLineLRec.VALIDATE("VAT Bus. Posting Group", SalesLineLRec."VAT Bus. Posting Group");
                                                                    //SAA3.0 >>
                                                                    IF ItemLLRec.GET(PromoOfferLineLRec."Gift Item No.") THEN;
                                                                    SalLineLRec.VALIDATE("Shortcut Dimension 3 Code", ItemLLRec."Shortcut Dimension 3 Code");
                                                                    SalLineLRec.VALIDATE("No.", PromoOfferLineLRec."Gift Item No.");
                                                                    SalLineLRec.VALIDATE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                    //RKD<<
                                                                    //IF SalesHeader2.GET(SalesLineLRec."Document Type",SalesLineLRec."Document No.") THEN
                                                                    //IF "Direct Sales Order" THEN B2B
                                                                    IF "Sales Type" = "Sales Type"::Direct then
                                                                        SalLineLRec."Location Code" := SalesLineLRec."Location Code"
                                                                    ELSE
                                                                        //RKD >>
                                                                        SalLineLRec."Location Code" := SalesLineLRec."Location Code"; //validation removed for lekki

                                                                    SalLineLRec.VALIDATE("Gift Item Quantity", PromoOfferLineLRec."Promotion Quantity");
                                                                    SalLineLRec.VALIDATE("Unit Price", 0);
                                                                    SalLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                    SalLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                                    SalLineLRec."Promo. Offer Line No." := PromoOfferLineLRec."Line No.";
                                                                    SalLineLRec."Gift Item" := TRUE;
                                                                    PromoGrpHeadLRec.SETRANGE("No.", PromoOfferLineLRec."Promo. Schd. Document No.");
                                                                    IF PromoGrpHeadLRec.FINDFIRST THEN
                                                                        //IF PromoGrpHeadLRec.GET(PromoOfferLineLRec."Promo. Schd. Document No.") THEN
                                                                        SalLineLRec.VALIDATE("Gen. Bus. Posting Group", PromoGrpHeadLRec."Gen. Bus. Posting Group");
                                                                    SalLineLRec.MODIFY;
                                                                END;
                                                                //UNTIL PromoOfferLineLRec.NEXT = 0;
                                                            END;
                                                        IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::Percentage THEN BEGIN
                                                            PromoOfferLineLRec.RESET;
                                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                            PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                            PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                                            IF PromoOfferLineLRec.FINDSET THEN
                                                                REPEAT
                                                                    //Find Percentage and promo gift quantity
                                                                    //MultiplePercentage:= ROUND(((100 * PromoOfferLineLRec."Promotion Quantity")
                                                                    /// PromoOfferLineLRec."Min. Quantity"),1);
                                                                    //PromoGiftQty := ROUND(MultiplePercentage * TotalOrdRemQty / 100,1,'<');
                                                                    PromoGiftQty := ROUND(((10 * PromoOfferLineLRec."Promotion Quantity") / 100)
                                                                          * TotalQtySold, 1);


                                                                    SalLineLRec.RESET;
                                                                    SalLineLRec.SETRANGE("Document Type", "Document Type");
                                                                    SalLineLRec.SETRANGE("Document No.", "No.");
                                                                    SalLineLRec.SETRANGE(Type, SalLineLRec.Type::Item);
                                                                    SalLineLRec.SETFILTER("No.", PromoOfferLineLRec."Gift Item No.");
                                                                    SalLineLRec.SETRANGE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                    SalLineLRec.SETRANGE("Gift Item", TRUE);
                                                                    IF SalLineLRec.FINDFIRST THEN BEGIN
                                                                        //SalLineLRec."Gift Item Quantity" += PromoOfferLineLRec."Promotion Quantity";
                                                                        //SalLineLRec.VALIDATE("Gift Item Quantity");
                                                                        //SalLineLRec.VALIDATE("Unit Price",0);
                                                                        //SalLineLRec.MODIFY(TRUE);
                                                                    END ELSE BEGIN
                                                                        SalLineLRec.INIT;
                                                                        SalLineLRec."Document Type" := SalesLineLRec."Document Type";
                                                                        SalLineLRec."Document No." := SalesLineLRec."Document No.";
                                                                        ReturnSalesLineNo(SalesLineLRec, PromoSchdLineLRec."Document No.", PromoSchdLineLRec."Line No.");
                                                                        SalLineLRec."Line No." := (LineNoGVar + 100);
                                                                        //SalLineLRec.VALIDATE("Sell-to Customer No.",SalesLineLRec."Sell-to Customer No.");
                                                                        //SalLineLRec."Shortcut Dimension 1 Code":=SalesLineLRec."Shortcut Dimension 1 Code";
                                                                        //SalLineLRec."Shortcut Dimension 2 Code":=SalesLineLRec."Shortcut Dimension 2 Code";
                                                                        //GJ_CHIPOS_RKD_221013 >>>>
                                                                        IF "POS Window" THEN
                                                                            SalLineLRec.VALIDATE("POS Window", TRUE);
                                                                        //GJ_CHIPOS_RKD_221013 <<<<

                                                                        SalLineLRec.INSERT;
                                                                        SalLineLRec.VALIDATE("Sell-to Customer No.", SalesLineLRec."Sell-to Customer No.");
                                                                        SalLineLRec.VALIDATE(Type, SalesLineLRec.Type);
                                                                        //SalLineLRec.VALIDATE("Shortcut Dimension 1 Code",SalesLineLRec."Shortcut Dimension 1 Code");
                                                                        //SalLineLRec.VALIDATE("Shortcut Dimension 2 Code",SalesLineLRec."Shortcut Dimension 2 Code");
                                                                        //SAA3.0 >>
                                                                        SalLineLRec.VALIDATE("Item Category Code", SalesLineLRec."Item Category Code");
                                                                        //SalLineLRec.VALIDATE("Product Group Code", SalesLineLRec."Product Group Code");//B2B
                                                                        SalLineLRec.VALIDATE("VAT Bus. Posting Group", SalesLineLRec."VAT Bus. Posting Group");
                                                                        //SAA3.0 >>
                                                                        IF ItemLLRec.GET(PromoOfferLineLRec."Gift Item No.") THEN;
                                                                        SalLineLRec.VALIDATE("Shortcut Dimension 3 Code", ItemLLRec."Shortcut Dimension 3 Code");
                                                                        SalLineLRec.VALIDATE("No.", PromoOfferLineLRec."Gift Item No.");
                                                                        SalLineLRec.VALIDATE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                        //RKD<<
                                                                        //IF SalesHeader2.GET(SalesLineLRec."Document Type",SalesLineLRec."Document No.") THEN
                                                                        //IF "Direct Sales Order" THEN  B2B
                                                                        IF "Sales Type" = "Sales Type"::Direct then
                                                                            SalLineLRec."Location Code" := SalesLineLRec."Location Code"
                                                                        ELSE
                                                                            //RKD >>
                                                                            SalLineLRec."Location Code" := SalesLineLRec."Location Code";  //removed validation for lekki
                                                                                                                                           //SalLineLRec.VALIDATE("Gift Item Quantity",PromoOfferLineLRec."Promotion Quantity");
                                                                        SalLineLRec.VALIDATE("Gift Item Quantity", PromoGiftQty);//SAA3.0
                                                                        SalLineLRec.VALIDATE("Unit Price", 0);
                                                                        SalLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                        SalLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                                        SalLineLRec."Promo. Offer Line No." := PromoOfferLineLRec."Line No.";
                                                                        SalLineLRec."Gift Item" := TRUE;
                                                                        PromoGrpHeadLRec.SETRANGE("No.", PromoOfferLineLRec."Promo. Schd. Document No.");
                                                                        //GJ_CHIPOS_RKD_191013 >>>>
                                                                        IF "POS Window" THEN
                                                                            PromoGrpHeadLRec.SETRANGE("Retail Promo", TRUE)
                                                                        ELSE
                                                                            PromoGrpHeadLRec.SETRANGE("Retail Promo", FALSE);
                                                                        //GJ_CHIPOS_RKD_191013 <<<<
                                                                        IF PromoGrpHeadLRec.FINDFIRST THEN
                                                                            //IF PromoGrpHeadLRec.GET(PromoOfferLineLRec."Promo. Schd. Document No.") THEN
                                                                            SalLineLRec.VALIDATE("Gen. Bus. Posting Group", PromoGrpHeadLRec."Gen. Bus. Posting Group");
                                                                        SalLineLRec.MODIFY;
                                                                    END;
                                                                UNTIL PromoOfferLineLRec.NEXT = 0;
                                                        END;

                                                    END;
                                                    //  update gift items when the promo group is valid <<
                                                END;
                                            // Checking for Promotion Group is valid for Sales Order Qty. to update free gift items <<
                                            UNTIL NOT OfferValid;
                                        END ELSE
                                            IF PromoGrpHeadLRec."Promo Type" = PromoGrpHeadLRec."Promo Type"::Group THEN BEGIN
                                                // Checking Promo is valid for sales details as a group >>

                                                CLEAR(TotalOrdRemQty);
                                                OfferValid := TRUE;
                                                REPEAT
                                                    CLEAR(PromoExists);
                                                    MinQtyLeft := PromoGrpHeadLRec."Group Quantity";
                                                    TempMinQtyLeft := MinQtyLeft;
                                                    CLEAR(TotalOrdRemQty);
                                                    SalesLineLRec.RESET;
                                                    SalesLineLRec.SETCURRENTKEY(Type, "No.", "Variant Code", "Drop Shipment", "Location Code", "Document Type",
                                                                                "Shipment Date");
                                                    SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                    SalesLineLRec.SETRANGE("Document No.", "No.");
                                                    SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                    SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                    IF SalesLineLRec.FINDSET THEN BEGIN
                                                        REPEAT
                                                            PromoGrpLineLRec.RESET;
                                                            PromoGrpLineLRec.SETRANGE("Document Type", PromoGrpLineLRec."Document Type"::"Promo Group");
                                                            PromoGrpLineLRec.SETRANGE("Document No.", PromoSchdLineLRec."No.");
                                                            PromoGrpLineLRec.SETRANGE(Type, PromoGrpLineLRec.Type::Item);
                                                            //GJ_CHIPOS_RKD_191013 >>>>
                                                            IF "POS Window" THEN
                                                                PromoGrpLineLRec.SETRANGE("Retail Promo", TRUE)
                                                            ELSE
                                                                PromoGrpLineLRec.SETRANGE("Retail Promo", FALSE);
                                                            //GJ_CHIPOS_RKD_191013 <<<<
                                                            PromoGrpLineLRec.SETRANGE("No.", SalesLineLRec."No.");
                                                            IF PromoGrpLineLRec.FINDSET THEN BEGIN
                                                                // Checking Sales Remaining Qty & Changing into Promogroup Item UOM >>
                                                                SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");
                                                                ItemLRec.GET(SalesLineLRec."No.");
                                                                TotalOrdRemQty += (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                                GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpHeadLRec."Group UOM")) * SalesQty;
                                                                TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');

                                                                TotalQtySold := TotalOrdRemQty;//SAA3.0
                                                                                               // Checking Sales Remaining Qty & Changing into Promogroup Item UOM <<
                                                                PromoExists := TRUE;
                                                            END;
                                                        UNTIL (SalesLineLRec.NEXT = 0);

                                                        IF (MinQtyLeft <= 0) THEN
                                                            OfferValid := FALSE;
                                                        IF TotalOrdRemQty < MinQtyLeft THEN
                                                            OfferValid := FALSE;
                                                        MinQtyLeft -= TotalOrdRemQty;

                                                        // To skip loop if no promotion lines exists on sales order >>
                                                        IF NOT PromoExists THEN
                                                            OfferValid := FALSE;
                                                        // To skip loop if no promotion lines exists on sales order <<

                                                        IF OfferValid THEN BEGIN
                                                            SalesLineLRec.RESET;
                                                            SalesLineLRec.SETCURRENTKEY(Type, "No.", "Variant Code", "Drop Shipment", "Location Code", "Document Type",
                                                                                        "Shipment Date");
                                                            SalesLineLRec.SETRANGE("Document Type", "Document Type");
                                                            SalesLineLRec.SETRANGE("Document No.", "No.");
                                                            SalesLineLRec.SETRANGE(Type, SalesLineLRec.Type::Item);
                                                            SalesLineLRec.SETRANGE("Gift Item", FALSE);
                                                            IF SalesLineLRec.FINDSET THEN BEGIN
                                                                REPEAT
                                                                    PromoGrpLineLRec.RESET;
                                                                    PromoGrpLineLRec.SETRANGE("Document Type", PromoGrpLineLRec."Document Type"::"Promo Group");
                                                                    PromoGrpLineLRec.SETRANGE("Document No.", PromoSchdLineLRec."No.");
                                                                    PromoGrpLineLRec.SETRANGE(Type, PromoGrpLineLRec.Type::Item);
                                                                    PromoGrpLineLRec.SETRANGE("No.", SalesLineLRec."No.");
                                                                    //GJ_CHIPOS_RKD_191013 >>>>
                                                                    IF "POS Window" THEN
                                                                        PromoGrpLineLRec.SETRANGE("Retail Promo", TRUE)
                                                                    ELSE
                                                                        PromoGrpLineLRec.SETRANGE("Retail Promo", FALSE);
                                                                    //GJ_CHIPOS_RKD_191013 <<<<
                                                                    IF PromoGrpLineLRec.FINDSET THEN BEGIN
                                                                        // Checking Sales Remaining Qty & Changing into Promogroup Item UOM >>
                                                                        SalesQty := (SalesLineLRec.Quantity - SalesLineLRec."Promo Qty. Util.");
                                                                        ItemLRec.GET(SalesLineLRec."No.");
                                                                        TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                                        GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpHeadLRec."Group UOM")) * SalesQty;
                                                                        TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                                                        // Checking Sales Remaining Qty & Changing into Promogroup Item UOM <<

                                                                        IF TotalOrdRemQty <= TempMinQtyLeft THEN BEGIN
                                                                            TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpHeadLRec."Group UOM") /
                                                                                               GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                                               * TotalOrdRemQty;
                                                                            TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                                                        END ELSE BEGIN
                                                                            TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpHeadLRec."Group UOM") /
                                                                                               GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code"))
                                                                                               * TempMinQtyLeft;
                                                                            TotalOrdRemQty := ROUND(TotalOrdRemQty, 1.0, '=');
                                                                        END;

                                                                        IF TotalOrdRemQty <> 0 THEN BEGIN
                                                                            SalesLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                            SalesLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                                        END;
                                                                        SalesLineLRec."Promo Qty. Util." += TotalOrdRemQty;
                                                                        SalesLineLRec.MODIFY;

                                                                        TotalOrdRemQty := (GetQtyPerUnitOfMeasure(ItemLRec, SalesLineLRec."Unit of Measure Code") /
                                                                                        GetQtyPerUnitOfMeasure(ItemLRec, PromoGrpHeadLRec."Group UOM")) * TotalOrdRemQty;

                                                                        TempMinQtyLeft -= TotalOrdRemQty;
                                                                    END;
                                                                UNTIL (SalesLineLRec.NEXT = 0);

                                                                //To filter Promotion Type
                                                                PromoOfferLineLGRec.RESET;
                                                                PromoOfferLineLGRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                                PromoOfferLineLGRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                                PromoOfferLineLGRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                                //PromoOfferLineLGRec.SETFILTER("Min. Quantity",'<=%1',TotalOrdRemQty);
                                                                PromoOfferLineLGRec.SETRANGE(Active, TRUE);
                                                                IF PromoOfferLineLGRec.FINDFIRST THEN
                                                                    IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::"Fixed Quantity" THEN BEGIN
                                                                        PromoOfferLineLRec.RESET;
                                                                        PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                                        PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                                        PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                                        PromoOfferLineLRec.SETFILTER("Min. Quantity", '<=%1', TotalQtySold); //TotalOrdRemQty);
                                                                        PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                                                        PromoOfferLineLRec.ASCENDING(TRUE);
                                                                        IF PromoOfferLineLRec.FINDLAST THEN BEGIN

                                                                            SalLineLRec.RESET;
                                                                            SalLineLRec.SETRANGE("Document Type", "Document Type");
                                                                            SalLineLRec.SETRANGE("Document No.", "No.");
                                                                            SalLineLRec.SETRANGE(Type, SalLineLRec.Type);
                                                                            SalLineLRec.SETFILTER("No.", PromoOfferLineLRec."Gift Item No.");
                                                                            SalLineLRec.SETRANGE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                            SalLineLRec.SETRANGE("Gift Item", TRUE);
                                                                            IF SalLineLRec.FINDFIRST THEN BEGIN
                                                                                //SalLineLRec."Gift Item Quantity" += PromoOfferLineLRec."Promotion Quantity";
                                                                                //SalLineLRec.VALIDATE("Gift Item Quantity");
                                                                                //SalLineLRec.VALIDATE("Unit Price",0);
                                                                                //SalLineLRec.MODIFY(TRUE);
                                                                            END ELSE BEGIN
                                                                                SalLineLRec.INIT;
                                                                                SalLineLRec."Document Type" := SalesLineLRec."Document Type";
                                                                                SalLineLRec."Document No." := SalesLineLRec."Document No.";
                                                                                ReturnSalesLineNo(SalesLineLRec, PromoSchdLineLRec."Document No.", PromoSchdLineLRec."Line No.");
                                                                                SalLineLRec."Line No." := (LineNoGVar + 100);
                                                                                //SalLineLRec.VALIDATE("Sell-to Customer No.",SalesLineLRec."Sell-to Customer No.");
                                                                                //SalLineLRec."Shortcut Dimension 1 Code":=SalesLineLRec."Shortcut Dimension 1 Code";
                                                                                //SalLineLRec."Shortcut Dimension 2 Code":=SalesLineLRec."Shortcut Dimension 2 Code";

                                                                                SalLineLRec.INSERT;
                                                                                SalLineLRec.VALIDATE("Sell-to Customer No.", SalesLineLRec."Sell-to Customer No.");
                                                                                SalLineLRec.VALIDATE(Type, SalesLineLRec.Type);
                                                                                IF ItemLLRec.GET(PromoOfferLineLRec."Gift Item No.") THEN;
                                                                                //SalLineLRec.VALIDATE("Shortcut Dimension 1 Code",SalesLineLRec."Shortcut Dimension 1 Code");
                                                                                //SalLineLRec.VALIDATE("Shortcut Dimension 2 Code",SalesLineLRec."Shortcut Dimension 2 Code");
                                                                                //SalLineLRec.VALIDATE("Shortcut Dimension 3 Code",ItemLLRec."Shortcut Dimension 3 Code");
                                                                                //SAA3.0 >>
                                                                                SalLineLRec.VALIDATE("Item Category Code", SalesLineLRec."Item Category Code");
                                                                                //SalLineLRec.VALIDATE("Product Group Code", SalesLineLRec."Product Group Code");//B2B
                                                                                SalLineLRec.VALIDATE("VAT Bus. Posting Group", SalesLineLRec."VAT Bus. Posting Group");
                                                                                //SAA3.0 >>
                                                                                SalLineLRec.VALIDATE("No.", PromoOfferLineLRec."Gift Item No.");
                                                                                SalLineLRec.VALIDATE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                                //RKD<<
                                                                                //IF SalesHeader2.GET(SalesLineLRec."Document Type",SalesLineLRec."Document No.") THEN
                                                                                //IF "Direct Sales Order" THEN B2B
                                                                                IF "Sales Type" = "Sales Type"::Direct then
                                                                                    SalLineLRec."Location Code" := SalesLineLRec."Location Code"
                                                                                ELSE
                                                                                    //RKD >>
                                                                                    SalLineLRec."Location Code" := SalesLineLRec."Location Code";   //removed validation for lekki
                                                                                SalLineLRec.VALIDATE("Gift Item Quantity", PromoOfferLineLRec."Promotion Quantity");
                                                                                SalLineLRec.VALIDATE("Unit Price", 0);
                                                                                SalLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                                SalLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                                                SalLineLRec."Promo. Offer Line No." := PromoOfferLineLRec."Line No.";
                                                                                SalLineLRec."Gift Item" := TRUE;
                                                                                PromoGrpHeadLRec.SETRANGE("No.", PromoOfferLineLRec."Promo. Schd. Document No.");
                                                                                IF PromoGrpHeadLRec.FINDFIRST THEN BEGIN
                                                                                    //IF PromoGrpHeadLRec.GET(PromoOfferLineLRec."Promo. Schd. Document No.") THEN
                                                                                    SalLineLRec.VALIDATE("Gen. Bus. Posting Group", PromoGrpHeadLRec."Gen. Bus. Posting Group");
                                                                                END;
                                                                                SalLineLRec.MODIFY;
                                                                            END;
                                                                            //UNTIL PromoOfferLineLRec.NEXT = 0;
                                                                        END;
                                                                    END;
                                                                IF PromoOfferLineLGRec."Promotion Type" = PromoOfferLineLGRec."Promotion Type"::Percentage THEN BEGIN
                                                                    PromoOfferLineLRec.RESET;
                                                                    PromoOfferLineLRec.SETRANGE("Promo. Schd. Doc. Type", PromoSchdLRec."Document Type");
                                                                    PromoOfferLineLRec.SETRANGE("Promo. Schd. Document No.", PromoSchdLRec."No.");
                                                                    PromoOfferLineLRec.SETRANGE("Promo. Schd. Line No.", PromoSchdLineLRec."Line No.");
                                                                    PromoOfferLineLRec.SETRANGE(Active, TRUE);
                                                                    IF PromoOfferLineLRec.FINDSET THEN
                                                                        REPEAT
                                                                            //Find Percentage and promo gift quantity
                                                                            //MultiplePercentage:= ROUND(((10 * PromoOfferLineLRec."Promotion Quantity")/100)
                                                                            //    * PromoOfferLineLRec."Min. Quantity",1);
                                                                            //PromoGiftQty := ROUND(MultiplePercentage * TotalOrdRemQty / 100,1,'<');
                                                                            //PromoGiftQty := ROUND(PromoOfferLineLRec."Promotion Quantity" * TotalOrdRemQty,1,'<');

                                                                            PromoGiftQty := ROUND(((10 * PromoOfferLineLRec."Promotion Quantity") / 100)
                                                                                  * TotalQtySold, 1);

                                                                            SalLineLRec.RESET;
                                                                            SalLineLRec.SETRANGE("Document Type", "Document Type");
                                                                            SalLineLRec.SETRANGE("Document No.", "No.");
                                                                            SalLineLRec.SETRANGE(Type, SalLineLRec.Type);
                                                                            SalLineLRec.SETFILTER("No.", PromoOfferLineLRec."Gift Item No.");
                                                                            SalLineLRec.SETRANGE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                            SalLineLRec.SETRANGE("Gift Item", TRUE);
                                                                            IF SalLineLRec.FINDFIRST THEN BEGIN
                                                                                //SalLineLRec."Gift Item Quantity" += PromoOfferLineLRec."Promotion Quantity";
                                                                                //SalLineLRec.VALIDATE("Gift Item Quantity");
                                                                                //SalLineLRec.VALIDATE("Unit Price",0);
                                                                                //SalLineLRec.MODIFY(TRUE);
                                                                            END ELSE BEGIN
                                                                                SalLineLRec.INIT;
                                                                                SalLineLRec."Document Type" := SalesLineLRec."Document Type";
                                                                                SalLineLRec."Document No." := SalesLineLRec."Document No.";
                                                                                ReturnSalesLineNo(SalesLineLRec, PromoSchdLineLRec."Document No.", PromoSchdLineLRec."Line No.");
                                                                                SalLineLRec."Line No." := (LineNoGVar + 100);
                                                                                //SalLineLRec.VALIDATE("Sell-to Customer No.",SalesLineLRec."Sell-to Customer No.");
                                                                                //SalLineLRec."Shortcut Dimension 1 Code":=SalesLineLRec."Shortcut Dimension 1 Code";
                                                                                //SalLineLRec."Shortcut Dimension 2 Code":=SalesLineLRec."Shortcut Dimension 2 Code";

                                                                                SalLineLRec.INSERT;
                                                                                SalLineLRec.VALIDATE("Sell-to Customer No.", SalesLineLRec."Sell-to Customer No.");
                                                                                SalLineLRec.VALIDATE(Type, SalesLineLRec.Type);
                                                                                IF ItemLLRec.GET(PromoOfferLineLRec."Gift Item No.") THEN;
                                                                                //SalLineLRec.VALIDATE("Shortcut Dimension 1 Code",SalesLineLRec."Shortcut Dimension 1 Code");
                                                                                //SalLineLRec.VALIDATE("Shortcut Dimension 2 Code",SalesLineLRec."Shortcut Dimension 2 Code");
                                                                                //SalLineLRec.VALIDATE("Shortcut Dimension 3 Code",ItemLLRec."Shortcut Dimension 3 Code");
                                                                                //SAA3.0 >>
                                                                                SalLineLRec.VALIDATE("Item Category Code", SalesLineLRec."Item Category Code");
                                                                                //SalLineLRec.VALIDATE("Product Group Code", SalesLineLRec."Product Group Code");//B2B
                                                                                SalLineLRec.VALIDATE("VAT Bus. Posting Group", SalesLineLRec."VAT Bus. Posting Group");
                                                                                //SAA3.0 >>
                                                                                SalLineLRec.VALIDATE("No.", PromoOfferLineLRec."Gift Item No.");
                                                                                SalLineLRec.VALIDATE("Unit of Measure Code", PromoOfferLineLRec."Unit of Measure Code");
                                                                                //RKD<<
                                                                                //IF SalesHeader2.GET(SalesLineLRec."Document Type",SalesLineLRec."Document No.") THEN
                                                                                //IF "Direct Sales Order" THEN B2B
                                                                                IF "Sales Type" = "Sales Type"::Direct then
                                                                                    SalLineLRec."Location Code" := SalesLineLRec."Location Code"
                                                                                ELSE
                                                                                    //RKD >>
                                                                                    SalLineLRec."Location Code" := SalesLineLRec."Location Code";  //removed validation for lekki
                                                                                                                                                   //SalLineLRec.VALIDATE("Gift Item Quantity",PromoOfferLineLRec."Promotion Quantity");
                                                                                SalLineLRec.VALIDATE("Gift Item Quantity", PromoGiftQty);//SAA3.0
                                                                                SalLineLRec.VALIDATE("Unit Price", 0);
                                                                                SalLineLRec."Promo. No." := PromoSchdLRec."No.";
                                                                                SalLineLRec."Promo. Line No." := PromoSchdLineLRec."Line No.";
                                                                                SalLineLRec."Promo. Offer Line No." := PromoOfferLineLRec."Line No.";
                                                                                SalLineLRec."Gift Item" := TRUE;
                                                                                PromoGrpHeadLRec.SETRANGE("No.", PromoOfferLineLRec."Promo. Schd. Document No.");
                                                                                IF PromoGrpHeadLRec.FINDFIRST THEN
                                                                                    //IF PromoGrpHeadLRec.GET(PromoOfferLineLRec."Promo. Schd. Document No.") THEN
                                                                                    SalLineLRec.VALIDATE("Gen. Bus. Posting Group", PromoGrpHeadLRec."Gen. Bus. Posting Group");
                                                                                SalLineLRec.MODIFY;
                                                                            END;
                                                                        UNTIL PromoOfferLineLRec.NEXT = 0;
                                                                END;

                                                            END;
                                                        END;
                                                    END;
                                                UNTIL NOT OfferValid;
                                                // Checking Promo is valid for sales details as a group <<

                                            END;
                                    END;
                                END;
                        END;
                        // Updating the calculated Promotional Gift Qty. to Quantity field in Sales Lines. >>
                        IF AcctLocAllowed THEN BEGIN
                            SalLineLRec.RESET;
                            SalLineLRec.SETRANGE("Document Type", "Document Type");
                            SalLineLRec.SETRANGE("Document No.", "No.");
                            SalLineLRec.SETRANGE("Gift Item", TRUE);
                            IF SalLineLRec.FINDSET THEN
                                REPEAT
                                    SalLineLRec.VALIDATE("Gift Item", FALSE);
                                    //GJ_CHIPOS_RKD_211013 >>>>
                                    SalesHeader.RESET;
                                    SalesHeader.SETRANGE("Document Type", "Document Type");
                                    SalesHeader.SETRANGE("No.", "No.");
                                    SalesHeader.SETRANGE("POS Window", TRUE);
                                    IF SalesHeader.FINDFIRST THEN
                                        SalLineLRec."POS Window" := TRUE;
                                    //GJ_CHIPOS_RKD_211013 <<<<
                                    SalLineLRec.VALIDATE(Quantity, SalLineLRec."Gift Item Quantity");
                                    SalLineLRec.VALIDATE("Unit Price", 0);
                                    SalLineLRec.VALIDATE("Gift Item", TRUE);
                                    //SalLineLRec.MODIFY;//PKONAU4
                                    //PKONAU3>>
                                    Error('Order is qualified for Promotions. Pls calculate promotions.');
                                //PKONAU3<<
                                //SalLineLRec.MODIFY;//PKONAU4
                                //COMMIT;
                                UNTIL SalLineLRec.NEXT = 0;
                        END;
                    // Updating the Promotional Gift Qty. to Quantity field in Sales Lines. <<
                    UNTIL PromoSchdLineLRec.NEXT = 0;
                end;
            //end;//Prasanna
            UNTIL PromoSchdLRec.NEXT = 0;
        end
    end;

    procedure InitOutstandingQutyT(var SalesHdr: Record "Sales Header")//PKONOC28 Whole Object
    var
        SalLneLRec: Record "Sales Line";
        QtyGVar: Decimal;
        QtyShiped: Decimal;
        QtyInvoiced: Decimal;
        QtyShipNotInv: Decimal;
        PosalInvLne: Record "Sales Invoice Line";
        PosShiLne: Record "Sales Shipment Line";
    BEGIN
        Exit;  //PKONOC29
        Clear(QtyGVar);
        Clear(QtyShiped);
        Clear(QtyInvoiced);
        clear(QtyShipNotInv);
        if not (SalesHdr."Document Type" = SalesHdr."Document Type"::Order) then
            exit;

        SalLneLRec.reset;
        SalLneLRec.SetRange("Document No.", "No.");
        SalLneLRec.SetFilter(Type, '<>%1', SalLneLRec.type::"Charge (Item)");
        IF SalLneLRec.FindSet() then
            repeat
                QtyGVar += SalLneLRec.Quantity;
                QtyShipNotInv += (SalLneLRec."Qty. Shipped Not Invoiced");
                PosalInvLne.RESET;
                PosalInvLne.SetRange("Order No.", SalLneLRec."Document No.");
                PosalInvLne.SetRange("Order Line No.", SalLneLRec."Line No.");
                IF PosalInvLne.findset THEN
                    repeat
                        QtyInvoiced += PosalInvLne.Quantity;
                    until PosalInvLne.next = 0;

                PosShiLne.RESET;
                PosShiLne.SetRange("Order No.", SalLneLRec."Document No.");
                PosShiLne.SetRange("Order Line No.", SalLneLRec."Line No.");
                IF PosShiLne.findset THEN
                    repeat
                        QtyShiped += PosShiLne.Quantity;
                    until PosShiLne.next = 0;
            until SalLneLRec.next = 0;

        IF ((QtyGVar <> 0) AND (QtyShiped <> 0)) THEN BEGIN
            IF ((QtyInvoiced <> 0)) THEN BEGIN
                IF (QtyGVar = QtyInvoiced) then BEGIN
                    SalesHdr."Order Tracking" := SalesHdr."Order Tracking"::"Completely Invoiced";
                    SalesHdr.Modify;
                END ELSE BEGIN
                    SalesHdr."Order Tracking" := SalesHdr."Order Tracking"::"Partially Invoiced";
                    SalesHdr.Modify;
                END;
            END ELSE BEGIN
                IF (QtyShiped = QtyGVar) then BEGIN
                    SalesHdr."Order Tracking" := SalesHdr."Order Tracking"::"Completely Shipped";
                    SalesHdr.Modify;
                end
                ELSE BEGIN
                    SalesHdr."Order Tracking" := SalesHdr."Order Tracking"::"Partially Shipped";
                    SalesHdr.Modify;
                END;
            END;
        END ELSE BEGIN
            SalesHdr."Order Tracking" := SalesHdr."Order Tracking"::"Not Yet Shipped";
            SalesHdr.Modify;
        END;
    END;

    procedure CheckSrinkageCreditMemo(saleLpar: record "Sales Header")//B2BSPON22AU17
    var
        myInt: Integer;
        SalesCrmemo: Record "Sales Cr.Memo Header";
    begin
        SalesCrmemo.Reset();
        SalesCrmemo.SetRange("Sell-to Customer No.", saleLpar."Sell-to Customer No.");
        SalesCrmemo.Setfilter("Printable Comment 1", '%1', 'Shrinakge Allowance Rebate for ' + saleLpar."Rebate Period Code");
        SalesCrmemo.SetRange("Responsibility Center", saleLpar."Responsibility Center");
        if SalesCrmemo.FindFirst then
            ;// Error('Shrinkage Credit memo has been Posted %1..%2..%3', saleLpar."Sell-to Customer No.", 'Shrinakge Allowance Rebate for ' + saleLpar."Rebate Period Code", saleLpar."Responsibility Center");
    end;

    procedure CHECKSalesAppliesDocVal()//PKON22JA6
    Var
        CustledLVar: record "Cust. Ledger Entry";
    begin
        IF (("Document Type" = "Document Type"::Order) AND ("Applies-to Doc. No." = '') and ("Applies-to ID" <> '')) THEN BEGIN
            CustledLVar.RESET;
            CustledLVar.setrange("Customer No.", "Sell-to Customer No.");
            CustledLVar.setrange("Applies-to ID", "Applies-to ID");
            CustledLVar.SetFilter(Amount, '<>%1', 0);
            IF CustledLVar.FINDSET THEN
                IF CustledLVar.count = 1 then
                    error('Single Payment is applied with apply ID, Pls use Applies to document number and clear Applies to ID');
        end;
    end;


    //RFC#2024-08 >>>
    /// <summary>
    /// CheckMandValues.
    /// </summary>
    procedure CheckMandValues();
    var
        SalesLineRec: Record "Sales Line";
        DataTempHeadLRec: Record "Data Template Header_CHI";
        DataTempLineLRec: Record "Data Template Line_CHI";
        RecRefLVar: RecordRef;
        FieldRefLVar: FieldRef;
        Text0001: Label '%1 must not be empty.';
        Text0003: Label '%1 value %2 in the header does not match with the value %3 in data template %4';
        Text0004: Label '%1 must not be empty in line No. %2';
        Text0005: Label '%1 value %2 in the line does not match with the value %3 in data template %4';
        Text0006: Label 'There is no data template created for %1.';
        Text0007: Label 'There is one or no mandatory field specify in data template for %1.';
    begin
        DataTempLineLRec.RESET;
        DataTempLineLRec.SETRANGE(TableID, 36);
        DataTempLineLRec.SETRANGE(Type, DataTempLineLRec.Type::Field);

        if "Document Type" = "Document Type"::"Credit Memo" then
            DataTempLineLRec.SETRANGE("Document Type", DataTempLineLRec."Document Type"::"Credit Memo");

        if "Document Type" = "Document Type"::Order then
            DataTempLineLRec.SETRANGE("Document Type", DataTempLineLRec."Document Type"::Order);

        if "Document Type" = "Document Type"::"Return Order" then
            DataTempLineLRec.SETRANGE("Document Type", DataTempLineLRec."Document Type"::"Return Order");


        if DataTempLineLRec.FINDSET then begin
            repeat
                RecRefLVar.GETTABLE(Rec);
                FieldRefLVar := RecRefLVar.FIELD(DataTempLineLRec.FieldID);

                if DataTempLineLRec.Mandatory then begin
                    if DataTempLineLRec."Default Value" <> '' then begin
                        if FORMAT(FieldRefLVar.VALUE) <> DataTempLineLRec."Default Value" then
                            ERROR(Text0003,
                                  FieldRefLVar.NAME, FieldRefLVar.VALUE, DataTempLineLRec."Default Value", DataTempLineLRec."Data Template Code");
                    end else begin
                        if (FORMAT(FieldRefLVar.VALUE) = '') or (FORMAT(FieldRefLVar.VALUE) = '0') then
                            ERROR(Text0001, FieldRefLVar.NAME);
                    end;
                end else
                    ERROR(Text0007, TABLENAME);
            until DataTempLineLRec.NEXT = 0;
        end; /*ELSE
         ERROR(Text0006,TABLENAME);*/

        CLEAR(FieldRefLVar);
        CLEAR(RecRefLVar);
        SalesLineRec.RESET;
        SalesLineRec.SETRANGE("Document No.", "No.");
        if SalesLineRec.FINDSET then begin
            repeat
                DataTempLineLRec.RESET;
                DataTempLineLRec.SETRANGE(TableID, 37);
                DataTempLineLRec.SETRANGE(Type, DataTempLineLRec.Type::Field);

                if "Document Type" = "Document Type"::"Return Order" then
                    DataTempLineLRec.SETRANGE("Document Type", DataTempLineLRec."Document Type"::"Return Order");

                if "Document Type" = "Document Type"::"Credit Memo" then
                    DataTempLineLRec.SETRANGE("Document Type", DataTempLineLRec."Document Type"::"Credit Memo");

                if "Document Type" = "Document Type"::Order then
                    DataTempLineLRec.SETRANGE("Document Type", DataTempLineLRec."Document Type"::Order);


                if DataTempLineLRec.FINDSET then begin
                    repeat
                        RecRefLVar.GETTABLE(SalesLineRec);
                        FieldRefLVar := RecRefLVar.FIELD(DataTempLineLRec.FieldID);

                        if DataTempLineLRec.Mandatory then begin
                            if DataTempLineLRec."Default Value" <> '' then begin
                                if FORMAT(FieldRefLVar.VALUE) <> DataTempLineLRec."Default Value" then
                                    ERROR(Text0005,
                                          FieldRefLVar.NAME, FieldRefLVar.VALUE, DataTempLineLRec."Default Value", DataTempLineLRec."Data Template Code");
                            end else begin
                                if (FORMAT(FieldRefLVar.VALUE) = '') or (FORMAT(FieldRefLVar.VALUE) = '0') then
                                    ERROR(Text0004, FieldRefLVar.NAME, SalesLineRec."Line No.");
                            end;
                        end else
                            ERROR(Text0007, SalesLineRec.TABLENAME);
                    until DataTempLineLRec.NEXT = 0;
                end; /*ELSE
             ERROR(Text0006,SalesLineRec.TABLENAME);*/

            /*  if (SalesLineRec."FA Posting Type" = SalesLineRec."FA Posting Type"::"Acquisition Cost") then begin
                 SalesLineRec.TESTFIELD("Capex No.");
                 SalesLineRec.TESTFIELD("Capex Line No.");
             end; */

            until SalesLineRec.NEXT = 0;
        end;/* ELSE
         ERROR(Text005,SalesLineRec."Document Type", "Document No.");  */

    end;

    //RFC#2024-08 <<<
    //g2sAdded function to drop rebate discount 270324
    procedure CalcRebate(var SalesHead: Record "Sales Header")
    var
        rebateRecordTab: Record "Rebate Records";
        rebatePeriodCodes: Record "Rebate Period Codes";
        salesHeaderRec: Record "Sales header";
        RemainingDiscAmt: Decimal;

        kdCustFocus: Record "KD Cust. Focus Brands ";
        MonthlyRebateSetup: Record "Monthly Rebate Setup";
        salesLINErec, salesLINErecCopy : record "Sales Line";
        startDate: Code[20];
        InvoiceDiscount, RebateDiscountPayable, TotalSalesAmt : Decimal;
        salesHDR, salesHDR2 : Record "Sales Header";
        RebateAmtFound, BalRebateAmtFound : Boolean;
        count: Integer;
    // salesLINErec: record "Sales Line";
    begin
        InvoiceDiscount := 0;
        RebateDiscountPayable := 0;
        RemainingDiscAmt := 0;
        TotalSalesAmt := 0;
        RebateDiscountPayable := salesHDR.pullDiscount(Rec);
        if RebateDiscountPayable <> 0 then begin

            salesLINErec.Reset();
            salesLINErec.setrange("Document No.", Rec."No.");
            salesLINErec.Calcsums("Line Amount", Amount, "Inv. Discount Amount", "Amount Including VAT");
            TotalSalesAmt := salesLINErec.Amount;
            if RebateDiscountPayable > TotalSalesAmt then begin
                Error('The sub total %1 must be more than the variable rebate %2', TotalSalesAmt, RebateDiscountPayable);
                RemainingDiscAmt := RebateDiscountPayable - TotalSalesAmt;
                // salesLINErec.Modifyall(Amount, 0);
                // salesLINErec.ModifyAll("Amount Including VAT", 0);
                // salesLINErec.ModifyAll("Outstanding Amount", 0);
                // salesLINErec.ModifyAll("Outstanding Amount (LCY)", 0);
                // salesLINErec.reset();
                // salesLINErec.setrange("Document No.", Rec."No.");
                salesLINErecCopy.Reset();
                salesLINErecCopy.setrange("Document No.", Rec."No.");
                if salesLINErecCopy.findfirst() then begin
                    repeat
                        // salesLINErecCopy."Rebate Discount" := ROUND((((salesLINErecCopy.Amount / TotalSalesAmt) * 100) / 100 * RebateDiscountPayable), 0.01);
                        salesLINErecCopy."Rebate Discount" := salesLINErecCopy.Amount;
                        // (((salesLINErecCopy.Amount / TotalSalesAmt) * 100) / 100 * TotalSalesAmt)
                        //>=(500/500 *100)/100 *1000
                        //////salesLINErecCopy."Inv. Discount Amount" += salesLINErecCopy."Rebate Discount";
                        //////salesLINErecCopy.Validate("Inv. Discount Amount");
                        //salesLINErecCopy."Inv. Disc. Amount to Invoice" += salesLINErecCopy."Rebate Discount";
                        ////// salesLINErecCopy.Amount := 0;
                        ////// salesLINErecCopy.Validate(Amount);
                        ///salesLINErecCopy."Amount Including VAT" := 0;
                        //salesLINErecCopy."Outstanding Amount" := 0;
                        //salesLINErecCopy."Outstanding Amount (LCY)" := 0;
                        salesLINErecCopy.Modify();
                    until salesLINErecCopy.next() = 0;
                end;
                if RemainingDiscAmt >= 0 then begin
                    rebateRecordTab.Reset();
                    rebateRecordTab.Setrange(rebateRecordTab."Customer No.", Rec."Sell-to Customer No.");
                    rebateRecordTab.Setfilter(rebateRecordTab."Posting Date", '<=%1', Rec."Posting Date");
                    rebateRecordTab.setrange(rebateRecordTab."Rebate Amount", RebateDiscountPayable);
                    if rebateRecordTab.findfirst() then begin
                        RebateAmtFound := true;
                        BalRebateAmtFound := false;
                    end else begin
                        rebateRecordTab.setrange(rebateRecordTab."Rebate Amount");
                        rebateRecordTab.setrange(rebateRecordTab."Balance Rebate Amount", RebateDiscountPayable);
                        if rebateRecordTab.findfirst() then begin
                            RebateAmtFound := false;
                            BalRebateAmtFound := true;
                        end;
                    end;
                    rebateRecordTab."No. of Transaction in a month" += 1;
                    rebateRecordTab."Balance Rebate Amount" := RemainingDiscAmt; //- InvoiceDiscount;
                    if rebateRecordTab."Balance Rebate Amount" = 0 then
                        rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Full else
                        rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Partial;
                    rebateRecordTab.modify();

                end;
            end else begin
                //salesLINErec."Inv. Discount Amount" += salesLINErec."Rebate Discount";
                //salesLINErec.Amount -= RebateDiscountPayable;
                //salesLINErec."Amount Including VAT" -= RebateDiscountPayable;
                salesLINErecCopy.Reset();
                salesLINErecCopy.SetRange("Document No.", Rec."No.");
                if salesLINErecCopy.findfirst() then begin
                    repeat
                        salesLINErecCopy.validate("Rebate Discount", ROUND(((salesLINErecCopy.Amount / TotalSalesAmt * 100) / 100 * RebateDiscountPayable), 0.01));
                        ////// salesLINErecCopy.Amount -= salesLINErecCopy."Rebate Discount";
                        ////// salesLINErecCopy.Validate(Amount);
                        //salesLINErecCopy."Amount Including VAT" -= salesLINErecCopy."Rebate Discount";
                        ////// salesLINErecCopy."Inv. Discount Amount" += salesLINErecCopy."Rebate Discount";
                        ////// salesLINErecCopy.Validate("Inv. Discount Amount");
                        // salesLINErecCopy."Inv. Disc. Amount to Invoice" += salesLINErecCopy."Rebate Discount";
                        // salesLINErecCopy."Outstanding Amount" -= salesLINErecCopy."Rebate Discount";
                        // salesLINErecCopy."Outstanding Amount (LCY)" -= salesLINErecCopy."Rebate Discount";
                        salesLINErecCopy.Modify;
                    until salesLINErecCopy.next() = 0;
                end;
                rebateRecordTab.Reset();
                rebateRecordTab.Setrange(rebateRecordTab."Customer No.", Rec."Sell-to Customer No.");
                rebateRecordTab.Setfilter(rebateRecordTab."Posting Date", '<=%1', Rec."Posting Date");
                rebateRecordTab.setrange(rebateRecordTab."Rebate Amount", RebateDiscountPayable);

                if rebateRecordTab.findfirst() then begin
                    RebateAmtFound := true;
                    BalRebateAmtFound := false;
                end else begin
                    rebateRecordTab.setrange(rebateRecordTab."Rebate Amount");
                    rebateRecordTab.setrange(rebateRecordTab."Balance Rebate Amount", RebateDiscountPayable);
                    if rebateRecordTab.findfirst() then begin
                        RebateAmtFound := false;
                        BalRebateAmtFound := true;
                    end;
                end;
                rebateRecordTab."No. of Transaction in a month" += 1;
                rebateRecordTab."Balance Rebate Amount" -= RebateDiscountPayable;
                if rebateRecordTab."Balance Rebate Amount" = 0 then
                    rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Full else
                    rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Partial;
                rebateRecordTab.modify();
            end;

        end;
    end;

    //for rebate calculation from quote to order
    procedure CalcRebateAmountonLines()
    var
        ReleaseSalesDoc: Codeunit "Release Sales Document";
        rebateRecordTab: Record "Rebate Records";
        rebatePeriodCodes: Record "Rebate Period Codes";
        salesHeaderRec: Record "Sales header";
        RemainingDiscAmt: Decimal;

        kdCustFocus: Record "KD Cust. Focus Brands ";
        MonthlyRebateSetup: Record "Monthly Rebate Setup";
        salesLINErec, salesLINErecCopy, salesLINErecCopy2 : record "Sales Line";
        startDate: Code[20];
        InvoiceDiscount, RebateDiscountPayable, TotalSalesAmt : Decimal;
        salesHDR, salesHDR2 : Record "Sales Header";
        RebateAmtFound, BalRebateAmtFound : Boolean;
        count: Integer;
        CustomSetup: Record "Custom Setup";
        CustSetupErr: Label 'KD rebate VAT setup is missing. Please check the setup.';
        CustSetupErrVAT: Label 'KD discount VAT combination is missing. Please check the VAT posting setup.';
        VATPostingSetup: Record "VAT Posting Setup";
        RebateLog: Record "Rebate Transaction Log";
    begin
        // CurrPage.update(true);
        InvoiceDiscount := 0;
        RebateDiscountPayable := 0;
        RemainingDiscAmt := 0;
        TotalSalesAmt := 0;
        RebateDiscountPayable := salesHDR.pullDiscount(Rec);

        if RebateDiscountPayable <> 0 then begin
            //newly added today 260424
            Rec."Initial Rebate Disc. Calc" := RebateDiscountPayable;
            Rec.Modify();

            RebateLog.SetCurrentKey("Order No."); //Application Entry
            RebateLog.SetRange("Order No.", Rec."No.");
            RebateLog.SetRange("Entry Type", RebateLog."Entry Type"::"Application Entry");
            if RebateLog.FindFirst() then
                exit;

            salesLINErec.Reset();
            salesLINErec.setrange("Document No.", Rec."No.");
            salesLINErec.Calcsums("Line Amount", Amount, "Inv. Discount Amount", "Amount Including VAT", AmtafterRebate, AmtafterRebateIncVAT);
            //290424
            if salesLINErec.AmtafterRebate <> 0 then
                TotalSalesAmt := salesLINErec.AmtafterRebate else
                TotalSalesAmt := salesLINErec.Amount;
            // TotalSalesAmt := salesLINErec."Amount Including VAT";
            //290424
            if RebateDiscountPayable > TotalSalesAmt then begin

                Error('The sub total %1 must be more than the variable rebate %2', TotalSalesAmt, RebateDiscountPayable);

                RemainingDiscAmt := RebateDiscountPayable - TotalSalesAmt;

                //rev sales Lines VAT
                CustomSetup.Reset();
                CustomSetup.SetRange(Category, CustomSetup.Category::"KD Rebate");
                CustomSetup.SetFilter("100% Disc. VAT Bus. Post Grp", '<>%1', '');
                CustomSetup.SetFilter("100% Disc. VAT Prod. Post Grp", '<>%1', '');
                if not CustomSetup.findfirst then Error(CustSetupErr);
                salesLINErecCopy2.Reset();
                salesLINErecCopy2.SetRange("Document No.", Rec."No.");
                //salesLINErec.SetRange("VAT Bus. Posting Group", CustomSetup."100% Disc. VAT Bus. Post Grp");
                salesLINErecCopy2.SetFilter("VAT Prod. Posting Group", '<>%1', CustomSetup."100% Disc. VAT Prod. Post Grp");
                if salesLINErecCopy2.findset then begin
                    VATPostingSetup.Reset();
                    VATPostingSetup.SetRange("VAT Bus. Posting Group", CustomSetup."100% Disc. VAT Bus. Post Grp");
                    VATPostingSetup.SetRange("VAT Prod. Posting Group", CustomSetup."100% Disc. VAT Prod. Post Grp");
                    if not VATPostingSetup.findfirst then ERROR(CustSetupErrVAT);
                    repeat
                        salesLINErecCopy2.Validate("VAT Prod. Posting Group", CustomSetup."100% Disc. VAT Prod. Post Grp");
                        salesLINErecCopy2.Modify();
                    until salesLINErecCopy2.Next() = 0;
                end;

                salesLINErecCopy.Reset();
                salesLINErecCopy.setrange("Document No.", Rec."No.");
                if salesLINErecCopy.findfirst() then begin
                    repeat
                        // salesLINErecCopy."Rebate Discount" := ROUND(((salesLINErecCopy.Amount / TotalSalesAmt * 100) / 100 * RebateDiscountPayable), 0.01) - salesLINErecCopy."Inv. Discount Amount";
                        //start 290424
                        if salesLINErecCopy.AmtafterRebate <> 0 then
                            salesLINErecCopy.validate("Rebate Discount", salesLINErecCopy.AmtafterRebate) else begin
                            salesLINErecCopy.validate("Rebate Discount", salesLINErecCopy.Amount);
                            // salesLINErecCopy.validate("Line Discount Amount", salesLINErecCopy.Amount);
                        end;


                        salesLINErecCopy.Modify();
                    until salesLINErecCopy.next() = 0;
                end;
                if RemainingDiscAmt >= 0 then begin
                    rebateRecordTab.Reset();
                    rebateRecordTab.Setrange(rebateRecordTab."Customer No.", Rec."Sell-to Customer No.");
                    //rebateRecordTab.Setfilter(rebateRecordTab."Posting Date", '<=%1', Rec."Posting Date");
                    rebateRecordTab.Setfilter(rebateRecordTab."Posting Date", '<=%1', Rec."Order Date");
                    rebateRecordTab.setrange(rebateRecordTab."Rebate Amount", RebateDiscountPayable);
                    if rebateRecordTab.findfirst() then begin
                        RebateAmtFound := true;
                        BalRebateAmtFound := false;
                    end else begin
                        rebateRecordTab.setrange(rebateRecordTab."Rebate Amount");
                        rebateRecordTab.setrange(rebateRecordTab."Balance Rebate Amount", RebateDiscountPayable);
                        if rebateRecordTab.findfirst() then begin
                            RebateAmtFound := false;
                            BalRebateAmtFound := true;
                        end;
                    end;
                    rebateRecordTab."No. of Transaction in a month" += 1;
                    rebateRecordTab."Balance Rebate Amount" := RemainingDiscAmt; //- InvoiceDiscount;
                    if rebateRecordTab."Balance Rebate Amount" = 0 then
                        rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Full else
                        rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Partial;
                    rebateRecordTab.modify();

                end;
            end else begin

                salesLINErecCopy.Reset();
                salesLINErecCopy.SetRange("Document No.", Rec."No.");
                if salesLINErecCopy.findfirst() then begin
                    repeat
                        //290424 start
                        if salesLINErecCopy.AmtafterRebate <> 0 then
                            salesLINErecCopy.validate("Rebate Discount", ROUND(((salesLINErecCopy.AmtafterRebate / TotalSalesAmt * 100) / 100 * RebateDiscountPayable), 0.01)) else begin
                            salesLINErecCopy.validate("Rebate Discount", ROUND(((salesLINErecCopy.Amount / TotalSalesAmt * 100) / 100 * RebateDiscountPayable), 0.01));
                            // salesLINErecCopy.validate("Line Discount Amount", "Rebate Discount");
                        end;

                        salesLINErecCopy.Modify;
                    until salesLINErecCopy.next() = 0;
                end;
                rebateRecordTab.Reset();
                rebateRecordTab.Setrange(rebateRecordTab."Customer No.", Rec."Sell-to Customer No.");
                rebateRecordTab.Setfilter(rebateRecordTab."Posting Date", '<=%1', Rec."Order Date");
                rebateRecordTab.setrange(rebateRecordTab."Rebate Amount", RebateDiscountPayable);
                if rebateRecordTab.findfirst() then begin
                    RebateAmtFound := true;
                    BalRebateAmtFound := false;
                end else begin
                    rebateRecordTab.setrange(rebateRecordTab."Rebate Amount");
                    rebateRecordTab.setrange(rebateRecordTab."Balance Rebate Amount", RebateDiscountPayable);
                    if rebateRecordTab.findfirst() then begin
                        RebateAmtFound := false;
                        BalRebateAmtFound := true;
                    end;
                end;
                rebateRecordTab."No. of Transaction in a month" += 1;
                rebateRecordTab."Balance Rebate Amount" -= RebateDiscountPayable;
                if rebateRecordTab."Balance Rebate Amount" = 0 then
                    rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Full else
                    rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Partial;
                rebateRecordTab.modify();

                RebateLog.Init();
                RebateLog."Order No." := Rec."No.";
                RebateLog."Customer No." := Rec."Sell-to Customer No.";
                RebateLog."Rebate Period" := rebateRecordTab."Rebate Period";
                RebateLog."Reabate Amount" := rebateRecordTab."Rebate Amount";
                RebateLog."Total Reabate Amount" := RebateDiscountPayable;
                RebateLog."Rebate Balance Amount" := rebateRecordTab."Balance Rebate Amount";
                RebateLog."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status";
                RebateLog."No. of Transaction in a Month" += 1;
                RebateLog."Posting Date" := Today;
                RebateLog."Applied By" := UserId();
                RebateLog."Entry Type" := RebateLog."Entry Type"::"Application Entry";
                RebateLog.Insert();
                Commit();
            end;
        end;

        // CurrPage.update(true);
    end;
    //Push rebate variable balance back to the rebate records
    Procedure ReturnRebateBalance()
    var
        RebateSalesLine: Record "Sales Line";
        rebatebalance, Totalrebate, TotalRebateAmount : Decimal;
        currencyrec: Record currency;
        rebatevariable: record "Rebate Records";
        RebateLog: Record "Rebate Transaction Log";
    begin
        rebatebalance := 0;
        Totalrebate := 0;
        TotalRebateAmount := 0;

        RebateLog.SetCurrentKey("Order No."); //Return Entry
        RebateLog.SetRange("Order No.", Rec."No.");
        RebateLog.SetRange("Entry Type", RebateLog."Entry Type"::"Return Entry");
        if RebateLog.FindFirst() then
            exit;

        RebateSalesLine.SetCurrentKey("Document No.", "Line No.");
        RebateSalesLine.setrange("document No.", Rec."No.");
        RebateSalesLine.SetFilter("Rebate Discount", '>%1', 0);
        //RebateSalesLine.CalcSums("Rebate Discount", Quantity, "Outstanding Quantity");
        if RebateSalesLine.FindSet() then
            repeat
                if RebateSalesLine."Rebate Discount" <> 0 then begin
                    if RebateSalesLine."Quantity Invoiced" <> RebateSalesLine.Quantity then begin
                        if RebateSalesLine."Outstanding Quantity" <> 0 then  //TODO Rebate Balance Calculation
                            rebatebalance := round(RebateSalesLine."Rebate Discount" *
                            (RebateSalesLine."Outstanding Quantity") / RebateSalesLine.quantity,
                            Currencyrec."Amount Rounding Precision") else
                            rebatebalance := round(RebateSalesLine."Rebate Discount" *
                            (RebateSalesLine.Quantity) / RebateSalesLine.quantity,
                            Currencyrec."Amount Rounding Precision");
                        Totalrebate += rebatebalance;
                    end;

                end;
            until RebateSalesLine.Next() = 0;

        if Totalrebate <> 0 then begin
            rebatevariable.reset;
            rebatevariable.setrange("Customer No.", rec."Sell-to Customer No.");
            if rebatevariable.FindLast() then begin
                rebatevariable."Balance Rebate Amount" += Totalrebate; //TODO Check this logic 
                If Totalrebate <> rebatevariable."Rebate Amount" then begin
                    rebatevariable."Rebate Discount Status" := rebatevariable."Rebate Discount Status"::Partial;
                    rebatevariable."No. of Transaction in a Month" += 1;
                end else
                    rebatevariable."Rebate Discount Status" := rebatevariable."Rebate Discount Status"::Full;
                rebatevariable.Modify();

                RebateLog.SetCurrentKey("Order No.");
                RebateLog.SetRange("Order No.", Rec."Quote No.");
                if RebateLog.FindFirst() then TotalRebateAmount := RebateLog."Total Reabate Amount";

                RebateLog.Reset();
                RebateLog.Init();
                RebateLog."Order No." := Rec."No.";
                RebateLog."Customer No." := Rec."Sell-to Customer No.";
                RebateLog."Rebate Period" := rebatevariable."Rebate Period";
                RebateLog."Reabate Amount" := rebatevariable."Rebate Amount";
                RebateLog."Total Reabate Amount" := TotalRebateAmount;
                RebateLog."Rebate Balance Amount" := Totalrebate;
                RebateLog."Rebate Discount Status" := rebatevariable."Rebate Discount Status";
                RebateLog."Return Count" += 1;
                RebateLog."Posting Date" := Today;
                RebateLog."Applied By" := UserId();
                RebateLog."Entry Type" := RebateLog."Entry Type"::"Return Entry";
                RebateLog.Insert();
                Commit();
            end;
        end;
    end;
    //check if rebate variable exists
    procedure RebateVariableexits(): Boolean
    var
        sLine: Record "Sales Line";
    begin

        SLine.setrange("document No.", Rec."No.");
        SLine.SetFilter("Rebate Discount", '>%1', 0);
        SLine.CalcSums("Rebate Discount");
        if sline."Rebate Discount" <> 0 then
            exit(true) else
            exit(false);
    end;

    /* procedure ReverseCalcRebateLines() // >>>>>> G2S 05/11/2024 CAS-01367-P9K8C9
    var
        ReleaseSalesDoc: Codeunit "Release Sales Document";
        rebateRecordTab: Record "Rebate Records";
        MonthlyRebateSetup: Record "Monthly Rebate Setup";
        salesLINErec, salesLINErecCopy, salesLINErecCopy2 : record "Sales Line";
        startDate: Code[20];
        InvoiceDiscount, RebateDiscountPayable, TotalSalesAmt, TotalRebateAmt, TotalReturnDiscount, ReturnDiscountPerLine
         : Decimal;
        salesHDR, salesHDR2 : Record "Sales Header";
        RebateAmtFound, BalRebateAmtFound, PartialInvoice : Boolean;
        count: Integer;
        CustomSetup: Record "Custom Setup";
        CustSetupErr: Label 'KD rebate VAT setup is missing. Please check the setup.';
        CustSetupErrVAT: Label 'KD discount VAT combination is missing. Please check the VAT posting setup.';
        VATPostingSetup: Record "VAT Posting Setup";
    BEGIN
        TotalRebateAmt := 0;
        TotalReturnDiscount := 0;
        PartialInvoice := False;
        //Since we have a rebate
        IF Rec."Initial Rebate Disc. Calc" <> 0 THEN BEGIN

            salesLINErec.Reset();
            salesLINErec.setrange("Document No.", Rec."No.");
            salesLINErec.Calcsums("Line Amount", Amount, "Inv. Discount Amount", "Amount Including VAT", AmtafterRebate, AmtafterRebateIncVAT);
            TotalSalesAmt := salesLINErec.Amount;
            //Sales Line Vat Prod. Posting Group

            //Return Rebate Discount
            salesLINErecCopy.Reset();
            salesLINErecCopy.setrange("Document No.", Rec."No.");
            if salesLINErecCopy.findfirst() then begin
                repeat
                    // salesLINErecCopy.CalcFields("Quantity Invoiced");
                    if salesLINErecCopy."Rebate Discount" > 0 then BEGIN
                        if salesLINErecCopy."Quantity Invoiced" > 0 THEN BEGIN
                            PartialInvoice := True;
                            ReturnDiscountPerLine := 0;
                            ReturnDiscountPerLine := (salesLINErecCopy."Fixed Rebate Amount") + (salesLINErecCopy."Rebate Disc. Amount to Inv.");
                            TotalReturnDiscount += ABS(salesLINErecCopy."Rebate Discount" - salesLINErecCopy."Rebate Disc. Amount to Inv.");//
                            salesLINErecCopy.Validate("Line Discount Amount", (salesLINErecCopy."Fixed Rebate Amount") + ReturnDiscountPerLine);
                            if salesLINErecCopy."Rebate Disc. Amount to Inv." <> 0 then
                                salesLINErecCopy.validate("Rebate Discount", salesLINErecCopy."Rebate Disc. Amount to Inv.") ELSE
                                salesLINErecCopy.validate("Rebate Discount", TotalRebateAmt);
                            salesLINErecCopy.Validate("Fixed Rebate Amount"); //Recalculate AfterRebateAmount
                        END ELSE begin
                            TotalRebateAmt += salesLINErecCopy."Rebate Discount";
                            salesLINErecCopy.Validate("Line Discount Amount", salesLINErecCopy."Fixed Rebate Amount");
                            salesLINErecCopy.validate("Rebate Discount", 0);
                            salesLINErecCopy.Validate("Fixed Rebate Amount"); //Recalculate AfterRebateAmount
                        end;
                        // salesLINErec.GetSalesHeader(Rec, Currency);
                        salesLINErecCopy.Modify();
                    END;
                until salesLINErecCopy.next() = 0;
            end;
            if (TotalRebateAmt > 0) OR (TotalReturnDiscount > 0) then begin
                rebateRecordTab.Reset();
                rebateRecordTab.Setrange(rebateRecordTab."Customer No.", Rec."Sell-to Customer No.");
                //rebateRecordTab.Setfilter(rebateRecordTab."Posting Date", '<=%1', Rec."Posting Date");
                rebateRecordTab.Setfilter(rebateRecordTab."Posting Date", '<=%1', Rec."Order Date");
                // rebateRecordTab.setrange(rebateRecordTab."Rebate Amount", RebateDiscountPayable);
                if rebateRecordTab.FindLast() then begin
                    IF NOT PartialInvoice THEN BEGIN
                        rebateRecordTab."No. of Transaction in a month" -= 1;
                        rebateRecordTab."Balance Rebate Amount" += TotalRebateAmt; //ReturnRebate
                        IF rebateRecordTab."No. of Transaction in a Month" > 0 then
                            rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Partial ELSE
                            rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Initial;
                    END ELSE BEGIN
                        rebateRecordTab."Balance Rebate Amount" += (TotalReturnDiscount); //ReturnRebate
                                                                                          // if rebateRecordTab."Balance Rebate Amount" <> 0 then
                        rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Partial;
                    END;
                    rebateRecordTab.modify();
                    Commit();
                end;
            end;
        end;
        // salesHDR.Reset();
        // salesHDR.Copy(Rec);
        // IF NOT PartialInvoice THEN
        //     salesHDR."Rebate Discount" := 0
        // ELSE
        //     salesHDR."Rebate Discount" := (Rec."Rebate Discount" - TotalReturnDiscount);
        // salesHDR.Validate("Rebate Discount");
        // salesHDR.Modify();
        // Commit();
        IF NOT PartialInvoice THEN
            REC."Rebate Discount" := 0
        ELSE
            REC."Rebate Discount" := TotalReturnDiscount;
        // REC.Validate("Rebate Discount");
        // REC.Modify();
        Commit();
    END; */

    var
        CustomerPostingGroup: Record "Customer Posting Group";
        UserCentrRespLRec: record "UserID Resp. Cent. Lines";
        UserCentrRespLRec2: record "UserID Resp. Cent. Lines";
        User: Record "User Setup";
        ItemUnitOfMeasure: Record "Item Unit of Measure";
        SalesQty: Decimal;
        NoOfferLineExists: Boolean;
        PromoOfferLineLGRec: Record "Promo. Schedule Offer Line";
        PromoOfferLineLRec: record "Promo. Schedule Offer Line";
        LineNoGVar: Integer;
        PromoGrpHeadLRec: Record "Promo Schedule";
        PromoExists: Boolean;
        OfferValid: Boolean;
        PromoGrpLineLRec: Record "Promo Schedule Line";
        SalesHeader: Record "Sales Header";
        AcctLocAllowed: Boolean;
        Text017: Label 'You must delete the existing sales lines before you can change %1.';
        Text50210: Label '%1 %2 lines must be Return Receipt Lines.';
        CustGRec: Record customer;
        Text50221: Label 'Please enter the POS Bank name';
        Text50222: Label 'Are sure the bank %1 selected is the POS Bank Name';

    // procedure ReverseCalcRebateLines() // >>>>>> G2S 05/11/2024 CAS-01367-P9K8C9
    // var
    //     ReleaseSalesDoc: Codeunit "Release Sales Document";
    //     rebateRecordTab: Record "Rebate Records";
    //     MonthlyRebateSetup: Record "Monthly Rebate Setup";
    //     salesLINErec, salesLINErecCopy, salesLINErecCopy2 : record "Sales Line";
    //     startDate: Code[20];
    //     InvoiceDiscount, RebateDiscountPayable, TotalSalesAmt, TotalRebateAmt, TotalReturnDiscount, TotalReturnPerc
    //      : Decimal;
    //     salesHDR, salesHDR2 : Record "Sales Header";
    //     RebateAmtFound, BalRebateAmtFound, PartialInvoice : Boolean;
    //     count: Integer;
    //     CustomSetup: Record "Custom Setup";
    //     CustSetupErr: Label 'KD rebate VAT setup is missing. Please check the setup.';
    //     CustSetupErrVAT: Label 'KD discount VAT combination is missing. Please check the VAT posting setup.';
    //     VATPostingSetup: Record "VAT Posting Setup";
    // BEGIN
    //     TotalRebateAmt := 0;
    //     TotalReturnDiscount := 0;
    //     //Since we have a rebate
    //     IF Rec."Initial Rebate Disc. Calc" <> 0 THEN BEGIN

    //         salesLINErec.Reset();
    //         salesLINErec.setrange("Document No.", Rec."No.");
    //         salesLINErec.Calcsums("Line Amount", Amount, "Inv. Discount Amount", "Amount Including VAT", AmtafterRebate, AmtafterRebateIncVAT);
    //         TotalSalesAmt := salesLINErec.Amount;
    //         //Sales Line Vat Prod. Posting Group

    //         //Return Rebate Discount
    //         salesLINErecCopy.Reset();
    //         salesLINErecCopy.setrange("Document No.", Rec."No.");
    //         if salesLINErecCopy.findfirst() then begin
    //             repeat
    //                 // salesLINErecCopy.CalcFields("Quantity Invoiced");
    //                 if salesLINErecCopy."Rebate Discount" > 0 then BEGIN
    //                     if salesLINErecCopy."Quantity Invoiced" > 0 THEN BEGIN
    //                         TotalReturnPerc := 0;
    //                         PartialInvoice := True;
    //                         TotalReturnPerc := (salesLINErecCopy."Quantity Invoiced" / salesLINErecCopy.Quantity);
    //                         TotalReturnDiscount += ABS((1 - TotalReturnPerc) * salesLINErecCopy."Rebate Discount");
    //                         // TotalReturnDiscount += ABS(salesLINErecCopy."Rebate Discount" - salesLINErecCopy."Rebate Disc. Amount to Inv.");
    //                         TotalRebateAmt += (salesLINErecCopy."Rebate Discount" * TotalReturnPerc);
    //                         if salesLINErecCopy."Rebate Disc. Amount to Inv." <> 0 then
    //                             salesLINErecCopy.validate("Rebate Discount", salesLINErecCopy."Rebate Disc. Amount to Inv.") ELSE
    //                             salesLINErecCopy.validate("Rebate Discount", TotalRebateAmt);
    //                         salesLINErecCopy.Validate("Fixed Rebate Amount"); //Recalculate AfterRebateAmount
    //                     END ELSE begin
    //                         PartialInvoice := False;
    //                         TotalRebateAmt += salesLINErecCopy."Rebate Discount";
    //                         salesLINErecCopy.validate("Rebate Discount", 0);
    //                         salesLINErecCopy.Validate("Fixed Rebate Amount"); //Recalculate AfterRebateAmount
    //                     end;
    //                     salesLINErecCopy.Modify();
    //                 END;
    //             until salesLINErecCopy.next() = 0;
    //         end;
    //         if TotalRebateAmt > 0 then begin
    //             rebateRecordTab.Reset();
    //             rebateRecordTab.Setrange(rebateRecordTab."Customer No.", Rec."Sell-to Customer No.");
    //             //rebateRecordTab.Setfilter(rebateRecordTab."Posting Date", '<=%1', Rec."Posting Date");
    //             rebateRecordTab.Setfilter(rebateRecordTab."Posting Date", '<=%1', Rec."Order Date");
    //             // rebateRecordTab.setrange(rebateRecordTab."Rebate Amount", RebateDiscountPayable);
    //             if rebateRecordTab.FindLast() then begin
    //                 IF NOT PartialInvoice THEN BEGIN
    //                     rebateRecordTab."No. of Transaction in a month" -= 1;
    //                     rebateRecordTab."Balance Rebate Amount" += TotalRebateAmt; //ReturnRebate
    //                     IF rebateRecordTab."No. of Transaction in a Month" > 0 then
    //                         rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Partial ELSE
    //                         rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Initial;
    //                 END ELSE BEGIN
    //                     rebateRecordTab."Balance Rebate Amount" += (Rec."Rebate Discount" - TotalReturnDiscount); //ReturnRebate
    //                                                                                                               // if rebateRecordTab."Balance Rebate Amount" <> 0 then
    //                     rebateRecordTab."Rebate Discount Status" := rebateRecordTab."Rebate Discount Status"::Partial;
    //                 END;
    //                 rebateRecordTab.modify();
    //             end;
    //         end;
    //     end;
    //     IF NOT PartialInvoice THEN BEGIN
    //         Rec."Rebate Discount" := 0;
    //         Rec.Modify();
    //     END;
    //     IF PartialInvoice THEN BEGIN
    //         Rec."Rebate Discount" += (Rec."Rebate Discount" - TotalReturnDiscount);
    //         Rec.Modify();
    //     END;
    // END;
}