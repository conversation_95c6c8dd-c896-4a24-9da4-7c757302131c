tableextension 50005 SaleRecTabExt311 extends "Sales & Receivables Setup"
{
    fields
    {
        field(50000; "Direct Sales Order"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50001; "Local Sales Order"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50002; "Export Sales Order"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50003; "Direct Sales Quote"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50004; "Local Sales Quote"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50005; "Export Sales Quote"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50006; "Validate Cust. Credit/Cash"; Boolean)
        {
            DataClassification = CustomerContent;

        }
        field(50007; "Loading SLip No."; Code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50008; "Posted Loading SLip No."; code[10])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50009; "Grace Period for Loading Slip"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50010; "Grace Period for Vehicle No."; Integer)
        {
            DataClassification = CustomerContent;
        }

        field(50011; "KD All Inv Rebate %"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50012; "KD Big Vol. Taget Price"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50013; "KD Big Vol. Taget Rebate %"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50014; "KD Small Vol. Taget Rebate%"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50015; "KD KBD Rebate %"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50016; "Quartarly Vol. Incentive %"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50017; "KBD RED Rebate Weight%"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'KBD RED Rebate Weight%';
        }
        field(50018; "KBD DMS Rebate Weight%"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'KBD DMS Rebate Weight%';
        }
        field(50019; "KBD Veh. Run Day RebateW%"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'KBD Veh. Run Day RebateW%';
        }
        field(50020; "KBD DMS Usage Days"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'KBD RED Min Score';
        }
        field(50021; "KBD Average OOS Target"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'KBD DMS Min Score';
        }
        field(50022; "KBD Veh. Run. Min Days"; Decimal)
        {
            DataClassification = CustomerContent;
            Description = 'KBD Veh. Run. Min Days';
        }
        field(50023; "Min Productivity Days"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50024; "Max Productivity Days"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(50025; "Redistribution GL Account"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
        }
        field(50026; "Cr. Limit Blocking Days"; DateFormula)
        {
            DataClassification = CustomerContent;
        }
        field(50027; "Promo Schedule No"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50028; "Calculate Promo On Cust. Grp."; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50030; "Preventive Negative Quantity"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50031; "XML File Path"; Text[250])
        {
            DataClassification = CustomerContent;
        }
        field(50032; "Self Lifting Credit Memo"; code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "G/L Account";
        }
        field(50033; "Customer Statement Mail CC1"; code[50])
        {
            DataClassification = CustomerContent;
        }
        field(50034; "Customer Statement Mail CC2"; code[50])
        {
            DataClassification = CustomerContent;
        }
        field(50035; "Sales By Volume"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50036; "Sales By Revenue"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50037; "Delete WareHouse Entry"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50038; "Enable InterSwitch"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50039; "Group Email Sales Notify"; Text[100])
        {
            DataClassification = CustomerContent;
        }
        field(50068; "Apply Promotions"; Boolean)
        {
            DataClassification = CustomerContent;//Balu 05092021
        }
        field(50069; "Pos Ship & Invoice"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50070; "Validate PSL in WHSHP"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(5007; "Mail On Invoice"; Boolean)
        {
            DataClassification = CustomerContent;//B2BPKON260521
        }
        field(50075; "Sales Complaints No."; Code[20])//PKON22J2-CR220070
        {
            DataClassification = CustomerContent;
            TableRelation = "No. Series";
        }
        field(50077; "Shrinkage %"; Decimal)//B2BSPON22AU17
        {
            DataClassification = CustomerContent;
            DecimalPlaces = 0 : 3;
        }
        //>>>>>> G2S CAS-01356-T3R1X0
        field(50078; "SalesVolume Report Checker"; Boolean)
        {
            DataClassification = ToBeClassified;
        }
        //<<<<<< G2S CAS-01356-T3R1X0
    }
}